package com.walnut.vegaspread.coa.model;

import jakarta.validation.constraints.NotNull;

import java.io.Serializable;
import java.util.Optional;

/**
 * DTO for {@link com.walnut.vegaspread.coa.entity.CoaEntity}
 */
public interface CoaEntityDto {

    record Create(@NotNull String coaText, @NotNull String coaDescription, @NotNull String lvl1Category,
                  @NotNull Boolean isActive, @NotNull Boolean sign) implements Serializable {
    }

    record Update(@NotNull int coaId, Optional<String> coaText, Optional<String> coaDescription,
                  Optional<String> lvl1Category, Optional<Boolean> isActive,
                  Optional<Boolean> sign) implements Serializable {
    }
}
