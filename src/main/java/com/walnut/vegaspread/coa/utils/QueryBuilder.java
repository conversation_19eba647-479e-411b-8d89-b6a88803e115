package com.walnut.vegaspread.coa.utils;

import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.model.ListTaskDto;
import com.walnut.vegaspread.coa.model.SortDto;
import com.walnut.vegaspread.coa.repository.CoaTaskRepository;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import io.quarkus.panache.common.Page;
import io.quarkus.panache.common.Sort;
import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class QueryBuilder {
    private static final String TASK_QUERY = " AND task.%s = :%s";
    private static final String TASK_ID = "taskId";
    private static final List<String> FILTER_COLUMNS = List.of("tableType", TASK_ID, "coaId", "entityId", "industryId",
            "regionId");
    private final ListTaskDto.GetTaskList taskList;
    private final CoaTaskRepository coaTaskRepository;

    @Getter
    private final HashMap<String, Object> params = new HashMap<>();
    @Getter
    private String query;

    public QueryBuilder(ListTaskDto.GetTaskList taskList, CoaTaskRepository coaTaskRepository) {
        this.taskList = taskList;
        this.coaTaskRepository = coaTaskRepository;
    }

    /**
     * Method to construct sort expression for query from a list of {@link com.walnut.vegaspread.coa.model.SortDto}
     */
    public static Sort sort(List<SortDto> sortDtos, SortDto defaultSortDto) {
        Sort sortQuery;
        Map<String, Sort.Direction> sortOrderMap = Map.of("ASC", Sort.Direction.Ascending,
                "DESC", Sort.Direction.Descending);

        if (!sortDtos.isEmpty()) {
            SortDto primarySort = sortDtos.get(0);
            String primarySortBy = primarySort.sortBy();

            //Construct query expression for first sort parameter
            sortQuery = Sort.by(primarySortBy, sortOrderMap.get(primarySort.sortType()));

            //Add sort for remaining parameters
            for (SortDto sortDto : sortDtos.stream().skip(1).toList()) {
                sortQuery.and(sortDto.sortBy(), sortOrderMap.get(sortDto.sortType()));
            }
        } else {
            sortQuery = Sort.by(defaultSortDto.sortBy(), sortOrderMap.get(defaultSortDto.sortType()));
        }
        return sortQuery;
    }

    private void filter(ListTaskDto.FilterDto filterDto) {
        String filterBy = filterDto.filterBy();
        if (!FILTER_COLUMNS.contains(filterBy)) {
            throw new IllegalArgumentException(String.format("Invalid filter type %s", filterBy));
        }
        String filterValue = filterDto.filterValue();
        switch (filterBy) {
            case "createdTime", "reviewedTime" ->
                    query += String.format(" AND DATE(task.%s) = :%s", filterBy, filterBy);
            case TASK_ID -> query += String.format(" AND task.id = :%s", filterBy);
            case "coaId" -> query += String.format(" AND task.coa.coaId = :%s", filterBy);
            case "entityId" -> query += String.format(" AND task.entityNameId = :%s", filterBy);
            case "industryId" -> query += String.format(" AND task.industryId = :%s", filterBy);
            case "regionId" -> query += String.format(" AND task.regionId = :%s", filterBy);
            default -> query += String.format(TASK_QUERY, filterBy, filterBy);
        }
        params.put(filterBy, filterValue);
    }

    private Sort sort() {
        Sort finalSort;
        Map<String, Sort.Direction> sortOrderMap = Map.of("ASC", Sort.Direction.Ascending,
                "DESC", Sort.Direction.Descending);

        List<ListTaskDto.SortDto> sortDtos = taskList.sort();
        if (!sortDtos.isEmpty()) {
            ListTaskDto.SortDto firstSort = sortDtos.get(0);
            String firstSortBy = firstSort.sortBy();
            if (Objects.equals(firstSortBy, TASK_ID)) {
                firstSortBy = "id";
            }
            finalSort = Sort.by(firstSortBy, sortOrderMap.get(firstSort.sortType()));
            for (ListTaskDto.SortDto sortDto : sortDtos.stream().skip(1).toList()) {
                finalSort.and(sortDto.sortBy(), sortOrderMap.get(sortDto.sortType()));
            }
        } else {
            finalSort = Sort.by("id", Sort.Direction.Descending);
        }
        return finalSort;
    }

    private void search() {
        List<String> searchColumns = FILTER_COLUMNS.stream().map(searchColumn -> {
            if (TASK_ID.equals(searchColumn))
                return "id";
            else if ("coaId".equals(searchColumn))
                return "coa.coaId";
            else
                return searchColumn;
        }).toList();
        query += String.format(" AND (CAST(task.%s AS text) LIKE :searchQuery", searchColumns.get(0));
        searchColumns.stream().skip(1)
                .forEach(col -> query += String.format(" OR CAST(task.%s AS text) LIKE :searchQuery", col));
        query += ")";
        params.put("searchQuery", String.format("%%%s%%", taskList.search()));
    }

    public List<CoaTaskEntity> listTasks(String clientName) {
        Sort finalSort = sort();

        query = "task.clientName = :clientName";
        params.put("clientName", clientName);

        for (ListTaskDto.FilterDto filterDto : taskList.filter()) {
            filter(filterDto);
        }
        if (!Objects.equals(taskList.search(), "")) {
            search();
        }

        query = String.format("SELECT task.id FROM CoaTaskEntity task WHERE %s", query);

        // Frontend sends pages starting from 1, not from 0
        PanacheQuery<TaskIdOnly> panacheQuery = coaTaskRepository.find(query, finalSort, params)
                .project(TaskIdOnly.class)
                .page(Page.of(taskList.pageNumber() - 1, taskList.pageSize()));
        List<TaskIdOnly> taskIds = panacheQuery.list();

        return coaTaskRepository.findByIds(taskIds.stream().map(TaskIdOnly::getId).toList(), finalSort);
    }

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    static class TaskIdOnly {
        Integer id;
    }
}
