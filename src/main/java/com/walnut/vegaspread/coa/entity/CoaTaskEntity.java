package com.walnut.vegaspread.coa.entity;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "coa_task")
public class CoaTaskEntity {

    public static final String ID_COL_NAME = "id";
    public static final String TABLE_TYPE_COL_NAME = "table_type";
    public static final String TEXT_COL_NAME = "text";
    public static final String FS_TEXT_COL_NAME = "fs_text";
    public static final String CLIENT_NAME_COL_NAME = "client_name";
    public static final String COA_FOREIGN_KEY_COL_NAME = "coa_id";
    public static final String ENTITY_NAME_ID_COL_NAME = "entity_name_id";
    public static final String INDUSTRY_ID_COL_NAME = "industry_id";
    public static final String REGION_ID_COL_NAME = "region_id";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ID_COL_NAME, nullable = false)
    public Integer id;

    @Column(name = TABLE_TYPE_COL_NAME, nullable = false, length = 255)
    public String tableType;

    @Column(name = TEXT_COL_NAME, nullable = false)
    public String text;

    @Column(name = FS_TEXT_COL_NAME, nullable = false)
    public String fsText;

    @Column(name = CLIENT_NAME_COL_NAME, nullable = false)
    public String clientName;

    @JsonUnwrapped
    @ManyToOne(optional = false)
    @ToString.Exclude
    @JoinColumn(name = COA_FOREIGN_KEY_COL_NAME, nullable = false)
    public CoaEntity coa;
    
    @Column(name = ENTITY_NAME_ID_COL_NAME, nullable = false)
    public Integer entityNameId;

    @Column(name = INDUSTRY_ID_COL_NAME, nullable = false)
    public Integer industryId;

    @Column(name = REGION_ID_COL_NAME, nullable = false)
    public Integer regionId;
}
