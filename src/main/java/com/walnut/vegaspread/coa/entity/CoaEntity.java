package com.walnut.vegaspread.coa.entity;

import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Objects;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
@Entity
@Table(name = CoaEntity.COA_LIST_TABLE_NAME)
public class CoaEntity {

    public static final String COA_LIST_TABLE_NAME = "coa_list";
    public static final String COA_ID_COL_NAME = "coa_id";
    public static final String COA_TEXT_COL_NAME = "coa_text";
    public static final String COA_DESCRIPTION_COL_NAME = "coa_description";
    public static final String CLIENT_NAME_COL_NAME = "client_name";
    public static final String IS_ACTIVE_COL_NAME = "is_active";
    public static final String LVL1_CATEGORY_COL_NAME = "lvl1_category";
    public static final String SIGN_COL_NAME = "sign";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = COA_ID_COL_NAME, nullable = false)
    public Integer coaId;

    @Column(name = COA_TEXT_COL_NAME, nullable = false, unique = true)
    public String coaText;

    @Column(name = COA_DESCRIPTION_COL_NAME, nullable = false)
    public String coaDescription;

    @Column(name = CLIENT_NAME_COL_NAME, nullable = false)
    public String clientName;

    @Column(name = LVL1_CATEGORY_COL_NAME, nullable = false)
    @Builder.Default
    public String lvl1Category = "";

    @Column(name = IS_ACTIVE_COL_NAME)
    public Boolean isActive;

    @Column(name = SIGN_COL_NAME)
    private Boolean sign;

    public CoaEntity(CoaEntity other) {
        this.coaId = other.coaId;
        this.coaText = other.coaText;
        this.coaDescription = other.coaDescription;
        this.clientName = other.clientName;
        this.lvl1Category = other.lvl1Category;
        this.isActive = other.isActive;
        this.sign = other.sign;
    }

    public static List<CoaItemDto> toDtoList(List<CoaEntity> coaEntities) {
        return coaEntities.stream()
                .map(CoaEntity::toDto)
                .toList();
    }

    public static CoaItemDto toDto(CoaEntity coaEntity) {
        return new CoaItemDto(
                coaEntity.getCoaId(),
                coaEntity.getCoaText(),
                coaEntity.getCoaDescription(),
                coaEntity.getClientName(),
                coaEntity.getLvl1Category(),
                coaEntity.getIsActive(),
                coaEntity.getSign()
        );
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CoaEntity coaEntity)) return false;
        return Objects.equals(getCoaId(), coaEntity.getCoaId()) && Objects.equals(getCoaText(),
                coaEntity.getCoaText()) && Objects.equals(getCoaDescription(),
                coaEntity.getCoaDescription()) && Objects.equals(getClientName(),
                coaEntity.getClientName()) && Objects.equals(getLvl1Category(),
                coaEntity.getLvl1Category()) && Objects.equals(getIsActive(), coaEntity.getIsActive());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getCoaId(), getCoaText(), getCoaDescription(), getClientName(), getLvl1Category(),
                getIsActive());
    }
}
