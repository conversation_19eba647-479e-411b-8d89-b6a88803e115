package com.walnut.vegaspread.coa.service;

import com.fasterxml.jackson.databind.MappingIterator;
import com.fasterxml.jackson.dataformat.csv.CsvMapper;
import com.fasterxml.jackson.dataformat.csv.CsvSchema;
import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.model.CoaEntityDto;
import com.walnut.vegaspread.coa.model.CoaListDto;
import com.walnut.vegaspread.coa.model.CoaUpdateUploadBean;
import com.walnut.vegaspread.coa.model.CoaUploadBean;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.Jwt.getClientName;

@ApplicationScoped
public class CoaService {

    private static final Logger logger = Logger.getLogger(CoaService.class);
    private final JsonWebToken accessToken;
    private final CoaRepository coaRepository;
    private final ExchangeService exchangeService;

    public CoaService(JsonWebToken accessToken, CoaRepository coaRepository, ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.coaRepository = coaRepository;
        this.exchangeService = exchangeService;
    }

    private static List<CoaEntityDto.Update> updateUploadBeansToUpdateDtos(
            List<CoaUpdateUploadBean> coaUpdateUploadBeans) {
        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        for (CoaUpdateUploadBean coaUpdateUploadBean : coaUpdateUploadBeans) {

            coaEntityUpdateDtos.add(new CoaEntityDto.Update(
                    coaUpdateUploadBean.getCoaId(),
                    Optional.of(coaUpdateUploadBean.getCoaText()),
                    Optional.of(coaUpdateUploadBean.getCoaDescription()),
                    Optional.of(coaUpdateUploadBean.getLvl1Category()),
                    Optional.of(coaUpdateUploadBean.getIsActive()),
                    Optional.of(coaUpdateUploadBean.getSign()))
            );
        }
        return coaEntityUpdateDtos;
    }

    @Transactional
    public List<CoaEntity> create(List<CoaEntityDto.Create> coaEntityCreateDtos, String clientName)
            throws NullPointerException {
        if (clientName == null) {
            throw new NullPointerException("clientName cannot be null");
        }
        List<CoaEntity> coaEntities = coaEntityCreateDtos.stream()
                .map(coaEntityCreateDto -> CoaEntity.builder()
                        .coaText(coaEntityCreateDto.coaText())
                        .coaDescription(coaEntityCreateDto.coaDescription())
                        .lvl1Category(coaEntityCreateDto.lvl1Category())
                        .isActive(coaEntityCreateDto.isActive())
                        .clientName(clientName)
                        .sign(coaEntityCreateDto.sign())
                        .build())
                .toList();
        coaRepository.persist(coaEntities);
        List<CoaItemAuditDto.Create> coaItemCreateAudits = coaEntities.stream()
                .flatMap(coaEntity -> Stream.of(
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaText),
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                                coaEntity.isActive.toString())
                ))
                .toList();
        exchangeService.coaItemAuditForCreate(coaItemCreateAudits);
        return coaEntities;
    }

    @Transactional
    public List<CoaEntity> update(List<CoaEntityDto.Update> coaEntityUpdateDtos) {
        if (coaEntityUpdateDtos == null || coaEntityUpdateDtos.isEmpty()) {
            return List.of();
        }
        List<CoaEntity> updatedCoaEntities = new ArrayList<>();
        List<CoaItemAuditDto.Update> coaItemRequests = new ArrayList<>();
        for (CoaEntityDto.Update coaEntityUpdateDto : coaEntityUpdateDtos) {
            CoaEntity coaEntity = coaRepository.findById(coaEntityUpdateDto.coaId());
            if (coaEntity == null) {
                logger.errorf("No COA found with id %s. Updated failed.", coaEntityUpdateDto.coaId());
            } else {
                coaEntityUpdateDto.coaText().flatMap(newCoaText -> Optional.ofNullable(
                        updateFieldAndAudit(newCoaText, coaEntity::getCoaText, coaEntity::setCoaText,
                                CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaId))).ifPresent(coaItemRequests::add);

                coaEntityUpdateDto.coaDescription()
                        .ifPresent(newCoaDescription -> updateFieldIfChanged(newCoaDescription,
                                coaEntity::getCoaDescription, coaEntity::setCoaDescription));

                coaEntityUpdateDto.isActive()
                        .flatMap(newIsActive -> Optional.ofNullable(
                                updateFieldAndAudit(newIsActive, coaEntity::getIsActive,
                                        coaEntity::setIsActive,
                                        CoaEntity.IS_ACTIVE_COL_NAME, coaEntity.getCoaId())))
                        .ifPresent(coaItemRequests::add);

                coaEntityUpdateDto.sign()
                        .flatMap(newSign -> Optional.ofNullable(
                                updateFieldAndAudit(newSign, coaEntity::getSign,
                                        coaEntity::setSign,
                                        CoaEntity.SIGN_COL_NAME, coaEntity.getCoaId())))
                        .ifPresent(coaItemRequests::add);

                coaEntityUpdateDto.lvl1Category()
                        .ifPresent(newLvl1Category -> updateFieldIfChanged(newLvl1Category, coaEntity::getLvl1Category,
                                coaEntity::setLvl1Category));

                updatedCoaEntities.add(coaEntity);
            }
        }
        exchangeService.coaItemAuditForUpdate(coaItemRequests);
        coaRepository.persist(updatedCoaEntities);
        return updatedCoaEntities;
    }

    private <T> void updateFieldIfChanged(T newValue, Supplier<T> getter, Consumer<T> setter) {
        if (!newValue.equals(getter.get())) {
            setter.accept(newValue);
        }
    }

    private <T> CoaItemAuditDto.Update updateFieldAndAudit(T newValue, Supplier<T> getter, Consumer<T> setter,
                                                           String fieldName, Integer coaId) {
        T oldValue = getter.get();
        if (!newValue.equals(oldValue)) {
            setter.accept(newValue);
            return new CoaItemAuditDto.Update(coaId, fieldName,
                    oldValue.toString(), newValue.toString());
        }
        return null;
    }

    public List<CoaEntity> list(String clientName, Boolean onlyActive) throws NullPointerException {
        String finalClientName = clientName == null ? getClientName(accessToken) : clientName;
        List<CoaEntity> coaList = Boolean.TRUE.equals(onlyActive) ? coaRepository.listActiveForClient(finalClientName)
                : coaRepository.listAllForClient(finalClientName);
        List<CoaEntity> commonCoaList = coaRepository.listAllForClient("common");
        commonCoaList.addAll(coaList);
        return commonCoaList;
    }

    public List<CoaEntity> listWithSortAndSearch(CoaListDto.GetSortAndSearchCoaList sortAndSearchDto, String clientName,
                                                 Boolean onlyActive) {
        String finalClientName = clientName == null ? getClientName(accessToken) : clientName;
        List<CoaEntity> coaList = Boolean.TRUE.equals(onlyActive) ? coaRepository.listActiveForClient(finalClientName,
                sortAndSearchDto)
                : coaRepository.listAllForClient(finalClientName, sortAndSearchDto);
        List<CoaEntity> commonCoaList = coaRepository.listAllForClient("common", sortAndSearchDto);
        coaList.addAll(commonCoaList);
        return coaList;
    }

    @Transactional
    public void delete(Integer coaId) {
        CoaEntity coaEntity = coaRepository.findById(coaId);
        List<CoaItemAuditDto.Delete> coaItemRequests = List.of(
                new CoaItemAuditDto.Delete(coaEntity.coaId, CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaText),
                new CoaItemAuditDto.Delete(coaEntity.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                        coaEntity.isActive.toString()));
        exchangeService.coaItemAuditForDelete(coaItemRequests);
        coaEntity.isActive = Boolean.FALSE;
        coaRepository.persist(coaEntity);
    }

    @Transactional
    public List<CoaUploadBean> uploadCsv(FileUpload file) throws IOException {

        List<CoaUploadBean> coaUploadBeans;
        try (MappingIterator<CoaUploadBean> coaUploadBeanIterator = new CsvMapper().readerFor(CoaUploadBean.class)
                .with(CsvSchema.emptySchema().withHeader())
                .readValues(file.filePath().toFile())) {
            coaUploadBeans = coaUploadBeanIterator.readAll();
        }
        List<CoaEntity> coaEntities = coaUploadBeans.stream().map(coaUploadBean -> CoaEntity.builder()
                .coaText(coaUploadBean.getCoaText())
                .coaDescription(coaUploadBean.getCoaDescription())
                .clientName(coaUploadBean.getClientName())
                .lvl1Category(coaUploadBean.getLvl1Category())
                .isActive(coaUploadBean.getIsActive())
                .sign(coaUploadBean.getSign()).build()).toList();

        coaRepository.persist(coaEntities);

        List<CoaItemAuditDto.Create> coaItemRequests = coaEntities.stream()
                .flatMap(coaEntity -> Stream.of(
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.COA_TEXT_COL_NAME, coaEntity.coaText),
                        new CoaItemAuditDto.Create(coaEntity.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                                coaEntity.isActive.toString())
                ))
                .toList();
        exchangeService.coaItemAuditForCreate(coaItemRequests);

        return coaUploadBeans;
    }

    public List<CoaItemDto> get(List<Integer> coaIds) {
        if (coaIds == null || coaIds.isEmpty()) {
            return Collections.emptyList();
        }
        return CoaEntity.toDtoList(coaRepository.findByIds(coaIds));
    }

    @Transactional
    public List<CoaEntity> uploadUpdateCsv(FileUpload file) throws IOException {

        List<CoaUpdateUploadBean> coaUpdateUploadBeans;
        try (MappingIterator<CoaUpdateUploadBean> coaUpdateUploadBeanIterator = new CsvMapper().readerFor(
                        CoaUpdateUploadBean.class)
                .with(CsvSchema.emptySchema().withHeader())
                .readValues(file.filePath().toFile())) {

            coaUpdateUploadBeans = coaUpdateUploadBeanIterator.readAll();
        }

        Set<Integer> updateCoaIds = coaUpdateUploadBeans.stream()
                .map(CoaUpdateUploadBean::getCoaId)
                .collect(Collectors.toSet());

        List<CoaEntity> coaEntities = coaRepository.findByIds(new ArrayList<>(updateCoaIds));

        if (coaEntities.size() != coaUpdateUploadBeans.size()) {
            Set<Integer> existingCoaIds = coaEntities.stream().map(CoaEntity::getCoaId).collect(Collectors.toSet());
            updateCoaIds.removeAll(existingCoaIds);
            throw new NoSuchElementException("Missing coa id/s for update: " + updateCoaIds);
        }

        List<CoaEntityDto.Update> coaEntityUpdateDtos = updateUploadBeansToUpdateDtos(coaUpdateUploadBeans);

        return update(coaEntityUpdateDtos);
    }
}
