package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.model.CoaMappingDto;
import com.walnut.vegaspread.coa.repository.CoaTaskRepository;
import io.quarkus.hibernate.orm.panache.PanacheQuery;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;

import static com.walnut.vegaspread.common.utils.Jwt.getClientName;

@ApplicationScoped
public class CoaMappingService {

    private final JsonWebToken accessToken;
    private final CoaTaskRepository coaTaskRepository;

    public CoaMappingService(JsonWebToken accessToken, CoaTaskRepository coaTaskRepository) {
        this.accessToken = accessToken;
        this.coaTaskRepository = coaTaskRepository;
    }

    public List<CoaMappingDto> getCoaMapping(String clientName) {
        String finalClientName = clientName == null ? getClientName(accessToken) : clientName;
        PanacheQuery<CoaTaskEntity> taskQuery = coaTaskRepository.find("clientName LIKE ?1",
                "%" + finalClientName + "%");
        return taskQuery.stream()
                .map(coaTaskEntity -> new CoaMappingDto(coaTaskEntity.tableType, coaTaskEntity.text,
                        coaTaskEntity.fsText, coaTaskEntity.coa.coaId))
                .toList();
    }
}
