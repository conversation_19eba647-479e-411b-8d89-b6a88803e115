package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.WalnutClientCoaMappingEntity;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.primarykey.WalnutClientCoaMappingPk;
import com.walnut.vegaspread.coa.repository.WalnutClientCoaMappingRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class WalnutClientCoaMappingService {

    private final WalnutClientCoaMappingRepository walnutClientCoaMappingRepository;

    public WalnutClientCoaMappingService(WalnutClientCoaMappingRepository walnutClientCoaMappingRepository) {
        this.walnutClientCoaMappingRepository = walnutClientCoaMappingRepository;
    }

    /**
     * Adds a list of new walnut coa and client coa mappings
     *
     * @param walnutClientCoaMappingEntities The id mappings for the coa.
     * @param clientName                     The name of the client for the mappings.
     * @return The list of newly created mappings.
     */
    @Transactional
    public List<WalnutClientCoaMappingEntity> create(
            List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> walnutClientCoaMappingEntities, String clientName) {
        if (walnutClientCoaMappingEntities == null || walnutClientCoaMappingEntities.isEmpty()) {
            return Collections.emptyList();
        }

        deleteExistingMappings(walnutClientCoaMappingEntities.stream()
                .map(WalnutClientCoaMappingEntityDto.CreateOrUpdate::walnutCoaId)
                .distinct()
                .toList(), clientName);

        List<WalnutClientCoaMappingEntity> mappings = walnutClientCoaMappingEntities.stream()
                .filter(walnutClientCoaMappingEntity -> walnutClientCoaMappingEntity.clientCoaId() != null && walnutClientCoaMappingEntity.walnutCoaId() != null)
                .map(walnutClientCoaMappingEntity -> WalnutClientCoaMappingEntity.builder()
                        .mapping(new WalnutClientCoaMappingPk(walnutClientCoaMappingEntity.walnutCoaId(),
                                walnutClientCoaMappingEntity.clientCoaId(), clientName))
                        .build())
                .toList();
        walnutClientCoaMappingRepository.persist(mappings);
        return mappings;
    }

    /**
     * Delete existing mapping using walnut coa id and client name;
     *
     * @param walnutCoaIds List of walnut coa id's for deleting mappings for the client.
     * @param clientName   The name of the client for which to delete the mapping.
     */
    private void deleteExistingMappings(List<Integer> walnutCoaIds, String clientName) {
        walnutClientCoaMappingRepository.deleteByWalnutCoaIdAndClientName(walnutCoaIds, clientName);
        walnutClientCoaMappingRepository.flush();
    }

    /**
     * Gets a list of walnut coa and client coa mappings for the given client name.
     *
     * @param clientName The name of the client for which the mappings are requested.
     * @return The list of mappings for the client.
     */
    public List<WalnutClientCoaMappingEntity> get(String clientName) {
        if (clientName == null || clientName.isBlank()) {
            return Collections.emptyList();
        }
        return walnutClientCoaMappingRepository.findByClientName(clientName);
    }
}
