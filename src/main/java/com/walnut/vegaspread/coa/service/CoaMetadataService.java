package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.common.utils.Jwt;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.security.InvalidParameterException;
import java.util.List;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;

@ApplicationScoped
public class CoaMetadataService {

    private final CoaRepository coaRepository;
    private final JsonWebToken accessToken;

    public CoaMetadataService(CoaRepository coaRepository, JsonWebToken accessToken) {
        this.coaRepository = coaRepository;
        this.accessToken = accessToken;
    }

    public List<String> getClientList() {
        String userClient = Jwt.getClientName(accessToken);
        if (!userClient.equals(DEFAULT_CLIENT_NAME)) {
            return List.of(userClient);
        }
        return coaRepository.find("SELECT DISTINCT clientName FROM CoaEntity")
                .project(String.class)
                .stream()
                .flatMap(x -> Stream.of(x.split(",")))
                .distinct()
                .toList();
    }

    public List<String> getLvl1CategoriesforClient(String clientName) {
        if (!getClientList().contains(clientName)) {
            throw new InvalidParameterException("Client name " + clientName + " does not exist");
        } else {
            return coaRepository.find("SELECT DISTINCT lvl1Category FROM CoaEntity WHERE clientName LIKE ?1",
                    "%" + clientName + "%").project(String.class).list();
        }
    }
}
