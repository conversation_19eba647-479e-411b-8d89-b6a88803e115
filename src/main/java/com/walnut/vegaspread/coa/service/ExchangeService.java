package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.common.clients.AuditClient;
import com.walnut.vegaspread.common.clients.ClientFactory;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.cloud.CloudPlatform;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
public class ExchangeService {

    private final AuditClient auditClient;

    public ExchangeService(@ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName,
                           @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType,
                           @ConfigProperty(name = ConfigKeys.AWS_GATEWAY_URL) Optional<String> awsGatewayUrlOptional) {
        if (cloudProviderType.equals(CloudPlatform.GCP.getProvider())) {
            this.auditClient = ClientFactory.createClient(AuditClient.class, envName);
        } else {
            if (awsGatewayUrlOptional.isEmpty()) {
                throw new IllegalArgumentException("AWS Gateway URL must be provided for AWS cloud provider");
            }
            String awsGatewayUrl = awsGatewayUrlOptional.get();
            this.auditClient = ClientFactory.createAWSClient(AuditClient.class, envName, awsGatewayUrl);
        }
    }

    public List<CoaItemAuditDto.Response> coaItemAuditForCreate(List<CoaItemAuditDto.Create> coaItemCreateAudits) {
        return auditClient.coaItemAuditForCreate(coaItemCreateAudits);
    }

    public List<CoaItemAuditDto.Response> coaItemAuditForUpdate(List<CoaItemAuditDto.Update> coaItemRequests) {
        return auditClient.coaItemAuditForUpdate(coaItemRequests);
    }

    public List<CoaItemAuditDto.Response> coaItemAuditForDelete(List<CoaItemAuditDto.Delete> coaItemRequests) {
        return auditClient.coaItemAuditForDelete(coaItemRequests);
    }
}
