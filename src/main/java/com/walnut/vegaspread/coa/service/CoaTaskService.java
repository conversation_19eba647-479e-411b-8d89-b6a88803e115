package com.walnut.vegaspread.coa.service;

import com.walnut.vegaspread.coa.entity.CoaTaskEntity;
import com.walnut.vegaspread.coa.model.CoaTaskDto;
import com.walnut.vegaspread.coa.model.ListTaskDto;
import com.walnut.vegaspread.coa.model.TaskListOutputDto;
import com.walnut.vegaspread.coa.model.TaskListResponseDto;
import com.walnut.vegaspread.coa.model.TaskOutputDto;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.repository.CoaTaskRepository;
import com.walnut.vegaspread.coa.utils.QueryBuilder;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;

import java.util.List;
import java.util.Objects;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;
import static com.walnut.vegaspread.common.utils.Jwt.getClientName;

@ApplicationScoped
public class CoaTaskService {
    private final JsonWebToken accessToken;
    private final CoaRepository coaRepository;
    private final CoaTaskRepository coaTaskRepository;
    private final ExchangeService exchangeService;

    public CoaTaskService(JsonWebToken accessToken, CoaRepository coaRepository, CoaTaskRepository coaTaskRepository,
                          ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.coaRepository = coaRepository;
        this.coaTaskRepository = coaTaskRepository;
        this.exchangeService = exchangeService;
    }

    private TaskOutputDto taskToOutput(CoaTaskEntity task) {
        return new TaskOutputDto(task.id, task.tableType, task.coa.coaId);
    }

    @Transactional
    public CoaTaskEntity create(CoaTaskDto.NewTask newTask) {
        CoaTaskEntity coaTask = CoaTaskEntity.builder()
                .tableType(newTask.tableType())
                .text(newTask.text())
                .fsText(newTask.fsText())
                .clientName(getClientName(accessToken))
                .coa(coaRepository.findById(newTask.coaId()))
                .build();
        coaTaskRepository.persist(coaTask);
        return coaTask;
    }

    @Transactional
    public TaskOutputDto update(CoaTaskDto.UpdateTask updateTask) {
        CoaTaskEntity coaTask = coaTaskRepository.findById(updateTask.taskId());
        if (updateTask.coaId() != null && !Objects.equals(updateTask.coaId(), coaTask.coa.coaId)) {
            coaTask.coa = coaRepository.findById(updateTask.coaId());
        }
        coaTaskRepository.persist(coaTask);
        return taskToOutput(coaTask);
    }

    public TaskOutputDto get(Integer taskId) {
        return taskToOutput(coaTaskRepository.findById(taskId));
    }

    public TaskListResponseDto list(ListTaskDto.GetTaskList taskListDto, boolean isApiKeyAuthenticated) {
        String clientName = isApiKeyAuthenticated ? DEFAULT_CLIENT_NAME : getClientName(accessToken);
        QueryBuilder qb = new QueryBuilder(taskListDto, coaTaskRepository);
        List<CoaTaskEntity> tasks = qb.listTasks(clientName);

        List<TaskListOutputDto> outputTasks = tasks.stream()
                .map(task -> new TaskListOutputDto(
                        task.id, task.tableType, task.coa.coaId, task.text, task.fsText, task.industryId,
                        task.regionId, task.entityNameId))
                .toList();

        // Return total number of tasks after filtering
        int whereIndex = qb.getQuery().indexOf("FROM");
        String countQuery = qb.getQuery().substring(whereIndex);
        long totalTasks = coaTaskRepository.count(countQuery, qb.getParams());

        return new TaskListResponseDto(taskListDto.pageNumber(), taskListDto.pageSize(),
                (int) Math.ceil((double) totalTasks / taskListDto.pageSize()),
                (int) totalTasks, outputTasks);
    }
}
