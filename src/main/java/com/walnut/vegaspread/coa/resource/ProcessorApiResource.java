package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.model.CoaMappingDto;
import com.walnut.vegaspread.coa.model.ListTaskDto;
import com.walnut.vegaspread.coa.model.TaskListResponseDto;
import com.walnut.vegaspread.coa.model.WalnutClientCoaMappingEntityDto;
import com.walnut.vegaspread.coa.service.CoaMappingService;
import com.walnut.vegaspread.coa.service.CoaService;
import com.walnut.vegaspread.coa.service.CoaTaskService;
import com.walnut.vegaspread.coa.service.WalnutClientCoaMappingService;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Info;
import org.jboss.resteasy.reactive.RestQuery;

import java.util.List;

@OpenAPIDefinition(info = @Info(title = "Processor API authenticated by X-API-Key", version = "1.0"))
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/wise/processor")
@ApiKeyAuthenticate
public class ProcessorApiResource {
    private static final String MAPPING_PREFIX = "/mapping";
    private static final String CLIENT_COA_MAPPING = "/walnut-client-mapping";
    private static final String COA_TASK_PREFIX = "/task";
    private final CoaMappingService coaMappingService;
    private final CoaService coaService;
    private final WalnutClientCoaMappingService walnutClientCoaMappingService;
    private final CoaTaskService coaTaskService;

    public ProcessorApiResource(CoaMappingService coaMappingService, CoaService coaService,
                                WalnutClientCoaMappingService walnutClientCoaMappingService,
                                CoaTaskService coaTaskService) {
        this.coaMappingService = coaMappingService;
        this.coaService = coaService;
        this.walnutClientCoaMappingService = walnutClientCoaMappingService;
        this.coaTaskService = coaTaskService;
    }

    @Path(MAPPING_PREFIX)
    @GET
    public List<CoaMappingDto> getCoaMapping(@RestQuery @NotNull String clientName) {
        return coaMappingService.getCoaMapping(clientName);
    }

    @Path("/list")
    @GET
    public List<CoaEntity> list(@RestQuery @NotNull String clientName) throws NullPointerException {
        return coaService.list(clientName, true);
    }

    @Path(CLIENT_COA_MAPPING)
    @GET
    public List<WalnutClientCoaMappingEntityDto.CreateOrUpdate> get(@RestQuery String clientName) {
        return walnutClientCoaMappingService.get(clientName)
                .stream()
                .map(entity -> new WalnutClientCoaMappingEntityDto.CreateOrUpdate(entity.getMapping().walnutCoaId,
                        entity.getMapping().clientCoaId))
                .toList();
    }

    @Path(COA_TASK_PREFIX + "/list")
    @POST
    public TaskListResponseDto list(ListTaskDto.GetTaskList taskListDto) {
        return coaTaskService.list(taskListDto, true);
    }
}
