package com.walnut.vegaspread.vector.entity;

import com.walnut.vegaspread.vector.model.FsTagMappingDto;
import jakarta.persistence.Column;
import jakarta.persistence.ColumnResult;
import jakarta.persistence.ConstructorResult;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.SqlResultSetMapping;
import jakarta.persistence.Transient;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;

@SqlResultSetMapping(
        name = "FsTagDtoMapping",
        classes = @ConstructorResult(
                targetClass = FsTagMappingDto.class,
                columns = {
                        @ColumnResult(name = "block_id", type = Integer.class),
                        @ColumnResult(name = "doc_id", type = UUID.class),
                        @ColumnResult(name = "embedding", type = String.class),
                        @ColumnResult(name = "tag", type = String.class),
                        @ColumnResult(name = "entity_id", type = Integer.class),
                        @ColumnResult(name = "entity_name", type = String.class),
                        @ColumnResult(name = "spread_level", type = String.class),
                        @ColumnResult(name = "last_modified_time", type = LocalDateTime.class)
                }
        )
)
@Entity
@Getter
@Setter
@NoArgsConstructor
public class FsTagEntity {
    public static final String TABLE_NAME = "fs_tag";
    public static final String BLOCK_ID_COL_NAME = "block_id";
    public static final String ENTITY_ID_COL_NAME = "entity_id";
    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String LAST_MODIFIED_TIME_COL_NAME = "last_modified_time";
    public static final String TAG_COL_NAME = "tag";
    public static final String ENTITY_NAME_COL_NAME = "entity_name";
    public static final String SPREAD_LEVEL_COL_NAME = "spread_level";
    public static final String EMBEDDING_COL_NAME = "embedding";

    @Id
    @Column(name = BLOCK_ID_COL_NAME, nullable = false)
    private Integer blockId;

    @NotNull
    @Column(name = DOC_ID_COL_NAME, nullable = false)
    private UUID docId;

    @NotNull
    @Column(name = LAST_MODIFIED_TIME_COL_NAME, nullable = false)
    private LocalDateTime lastModifiedTime;

    @Size(max = 255)
    @NotNull
    @Column(name = TAG_COL_NAME, nullable = false)
    private String tag;

    @NotNull
    @Column(name = ENTITY_ID_COL_NAME, nullable = false)
    private Integer entityId;

    @Size(max = 255)
    @NotNull
    @Column(name = ENTITY_NAME_COL_NAME, nullable = false)
    private String entityName;

    @Size(max = 255)
    @NotNull
    @Column(name = SPREAD_LEVEL_COL_NAME, nullable = false)
    private String spreadLevel;

    /*Marked as transient since we are converting the embedding to/from string before persisting/fetching and entity
    does not support float[]. Since we are using native queries, we can persist the embedding as string.*/
    @Transient
    private float[] embedding;

    public FsTagEntity(
            Integer blockId,
            UUID docId,
            float[] embedding,
            String tag,
            Integer entityId,
            String entityName,
            String spreadLevel,
            LocalDateTime lastModifiedTime

    ) {
        this.setBlockId(blockId);
        this.setDocId(docId);
        this.setLastModifiedTime(lastModifiedTime);
        this.setTag(tag);
        this.setEntityId(entityId);
        this.setEntityName(entityName);
        this.setSpreadLevel(spreadLevel);
        this.embedding = embedding;
    }
}