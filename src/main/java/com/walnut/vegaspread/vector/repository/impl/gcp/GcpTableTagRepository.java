package com.walnut.vegaspread.vector.repository.impl.gcp;

import com.walnut.vegaspread.vector.entity.TableTagEntity;
import com.walnut.vegaspread.vector.model.TableTagDto;
import com.walnut.vegaspread.vector.model.TableTagMappingDto;
import com.walnut.vegaspread.vector.repository.spi.TableTagRepositoryInterface;
import com.walnut.vegaspread.vector.service.Utils;
import io.quarkus.arc.profile.UnlessBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
@UnlessBuildProfile("aws")
public class GcpTableTagRepository implements TableTagRepositoryInterface {
    private final EntityManager em;

    @Inject
    public GcpTableTagRepository(EntityManager em) {
        this.em = em;
    }

    @Override
    public TableTagDto.QueryResponse findSimilar(TableTagDto.Query queryDto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT ")
                .append(TableTagEntity.BLOCK_ID_COL_NAME).append(", ")
                .append(TableTagEntity.DOC_ID_COL_NAME).append(", ")
                .append(TableTagEntity.TAG_COL_NAME).append(", ")
                .append("1 - cosine_distance(")
                .append(TableTagEntity.EMBEDDING_COL_NAME)
                .append(", string_to_vector(:queryVector)) AS cos_score ")
                .append("FROM ")
                .append(TableTagEntity.TABLE_NAME).append(" ")
                .append("WHERE (1 - cosine_distance(")
                .append(TableTagEntity.EMBEDDING_COL_NAME)
                .append(", string_to_vector(:queryVector))) > :scoreThreshold ")
                .append("ORDER BY cosine_distance(")
                .append(TableTagEntity.EMBEDDING_COL_NAME)
                .append(", string_to_vector(:queryVector)) ")
                .append("LIMIT :count");

        List<TableTagMappingDto> results = em.createNativeQuery(queryBuilder.toString(), "TableTagDtoMapping")
                .setParameter("queryVector", Utils.toVectorString(queryDto.queryVector()))
                .setParameter("scoreThreshold", queryDto.scoreThreshold())
                .setParameter("count", queryDto.count())
                .getResultList();
        return new TableTagDto.QueryResponse(
                results.stream().map(result -> new TableTagDto.QueryResponseItem(result.blockId(), result.docId(),
                                result.tag(), result.cosScore()))
                        .toList());
    }

    @Override
    @Transactional
    public void persist(List<TableTagEntity> tableTags) {
        if (tableTags == null || tableTags.isEmpty()) {
            return;
        }

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("INSERT INTO ")
                .append(TableTagEntity.TABLE_NAME)
                .append(" (block_id, doc_id, last_modified_time, embedding, tag) VALUES ");

        // Build the VALUES part
        for (int i = 0; i < tableTags.size(); i++) {
            queryBuilder.append("(")
                    .append(":blockId").append(i).append(", ")
                    .append("UNHEX(REPLACE(:docId").append(i).append(", '-', '')), ")
                    .append("NOW(), ")
                    .append("string_to_vector(:embedding").append(i).append("), ")
                    .append(":tag").append(i)
                    .append(")");

            if (i < tableTags.size() - 1) {
                queryBuilder.append(", ");
            }
        }

        Query query = em.createNativeQuery(queryBuilder.toString());

        // Set all parameters
        for (int i = 0; i < tableTags.size(); i++) {
            TableTagEntity tag = tableTags.get(i);
            query.setParameter("blockId" + i, tag.getBlockId());
            query.setParameter("docId" + i, tag.getDocId().toString());
            query.setParameter("embedding" + i, Utils.toVectorString(tag.getEmbedding()));
            query.setParameter("tag" + i, tag.getTag());
        }

        query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByDocId(UUID docId) {
        String queryBuilder = String.format("DELETE FROM %s WHERE doc_id = UNHEX(REPLACE(:docId, '-', ''))",
                TableTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("docId", docId.toString());
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByBlockId(Integer blockId) {
        String queryBuilder = String.format("DELETE FROM %s WHERE block_id = :blockId",
                TableTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("blockId", blockId);
        return query.executeUpdate();
    }
}
