package com.walnut.vegaspread.vector.repository;

import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.vector.repository.spi.TableTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

@ApplicationScoped
public class TableTagRepositoryFactory {
    private static final Logger logger = Logger.getLogger(TableTagRepositoryFactory.class);
    private final String cloudProviderType;
    Instance<TableTagRepositoryInterface> tableTagRepositories;

    public TableTagRepositoryFactory(Instance<TableTagRepositoryInterface> tableTagRepositories,
                                     @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType) {
        this.tableTagRepositories = tableTagRepositories;
        this.cloudProviderType = cloudProviderType;
    }

    public TableTagRepositoryInterface getTableTagRepository() {
        for (TableTagRepositoryInterface tableTagRepository : tableTagRepositories) {
            if (tableTagRepository.getClass()
                    .getSimpleName()
                    .toLowerCase()
                    .startsWith(cloudProviderType.toLowerCase())) {
                return tableTagRepository;
            }
        }

        throw new IllegalStateException("No cloud provider repository configured for type: " + cloudProviderType);
    }
}
