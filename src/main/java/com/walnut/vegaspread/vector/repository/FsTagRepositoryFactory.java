package com.walnut.vegaspread.vector.repository;

import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

@ApplicationScoped
public class FsTagRepositoryFactory {
    private static final Logger logger = Logger.getLogger(FsTagRepositoryFactory.class);
    @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType
    Instance<FsTagRepositoryInterface> fsTagRepositories;

    public FsTagRepositoryFactory(Instance<FsTagRepositoryInterface> fsTagRepositories,
                                 ) {
        this.fsTagRepositories = fsTagRepositories;
       
    }

    public FsTagRepositoryInterface getFsTagRepository() {
        logger.info("fsTagRepositories:" + fsTagRepositories);
        for (FsTagRepositoryInterface fsTagRepository : fsTagRepositories) {
            logger.info("FsTagRepository: " + fsTagRepository.getClass().getSimpleName());
            logger.info("CloudProviderType: " + cloudProviderType.toLowerCase());
            if (fsTagRepository.getClass().getSimpleName().toLowerCase().startsWith(cloudProviderType.toLowerCase())) {
                return fsTagRepository;
            }
        }

        throw new IllegalStateException("No cloud provider repository configured for type: " + cloudProviderType);
    }
}
