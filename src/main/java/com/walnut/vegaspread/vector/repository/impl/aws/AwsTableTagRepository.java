package com.walnut.vegaspread.vector.repository.impl.aws;

import com.walnut.vegaspread.vector.entity.TableTagEntity;
import com.walnut.vegaspread.vector.model.TableTagDto;
import com.walnut.vegaspread.vector.model.TableTagMappingDto;
import com.walnut.vegaspread.vector.repository.spi.TableTagRepositoryInterface;
import com.walnut.vegaspread.vector.service.Utils;
import com.walnut.vegaspread.vector.utils.Config;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@ApplicationScoped
@IfBuildProfile("aws")
public class AwsTableTagRepository implements TableTagRepositoryInterface {
    private final String schema;
    private final EntityManager em;

    @Inject
    public AwsTableTagRepository(
            @ConfigProperty(name = Config.DEFAULT_SCHEMA) Optional<String> schema,
            EntityManager em) {
        if (schema.isEmpty()) {
            throw new IllegalStateException("Schema is not configured");
        }
        this.schema = schema.get();
        this.em = em;
    }

    @Override
    public TableTagDto.QueryResponse findSimilar(TableTagDto.Query queryDto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT ")
                .append(TableTagEntity.BLOCK_ID_COL_NAME)
                .append(",")
                .append(TableTagEntity.DOC_ID_COL_NAME)
                .append(",")
                .append(TableTagEntity.TAG_COL_NAME)
                .append(",")
                .append("(1 - (")
                .append(TableTagEntity.EMBEDDING_COL_NAME)
                .append(" <=> CAST(:queryVector AS vector))) AS cos_score ")
                .append("FROM ")
                .append(schema)
                .append(".")
                .append(TableTagEntity.TABLE_NAME)
                .append(" ")
                .append("WHERE (1 - (")
                .append(TableTagEntity.EMBEDDING_COL_NAME)
                .append(" <=> CAST(:queryVector AS vector))) > :scoreThreshold ")
                .append("ORDER BY ")
                .append(TableTagEntity.EMBEDDING_COL_NAME)
                .append(" <=> CAST(:queryVector AS vector) ")
                .append("LIMIT :count");

        String query = queryBuilder.toString();
        List<TableTagMappingDto> results = em.createNativeQuery(query, "TableTagDtoMapping")
                .setParameter("queryVector", Utils.toFloatArray(queryDto.queryVector()))
                .setParameter("scoreThreshold", queryDto.scoreThreshold())
                .setParameter("count", queryDto.count())
                .getResultList();
        return new TableTagDto.QueryResponse(results.stream().map(result -> new TableTagDto.QueryResponseItem(
                        result.blockId(), result.docId(), result.tag(), result.cosScore()))
                .toList());
    }

    @Override
    @Transactional
    public long deleteByDocId(UUID docId) {

        String queryBuilder = String.format("DELETE FROM %s.%s WHERE doc_id = :docId",
                schema,
                TableTagEntity.TABLE_NAME);

        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("docId", docId);
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByBlockId(Integer blockId) {
        String queryBuilder = String.format("DELETE FROM %s.%s WHERE block_id = :blockId",
                schema,
                TableTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("blockId", blockId);
        return query.executeUpdate();
    }

    @Transactional
    public void persist(List<TableTagEntity> tableTags) {
        if (tableTags == null || tableTags.isEmpty()) {
            return;
        }

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("INSERT INTO ")
                .append(schema)
                .append(".")
                .append(TableTagEntity.TABLE_NAME)
                .append(" (block_id, doc_id, last_modified_time, embedding, tag) VALUES ");

        // Build the VALUES part
        for (int i = 0; i < tableTags.size(); i++) {
            queryBuilder.append("(")
                    .append(":blockId").append(i).append(", ")
                    .append(":docId").append(i).append(", ")
                    .append("NOW(), ")
                    .append("vector(:embedding").append(i).append("), ")
                    .append(":tag").append(i)
                    .append(")");

            if (i < tableTags.size() - 1) {
                queryBuilder.append(", ");
            }
        }

        Query query = em.createNativeQuery(queryBuilder.toString());

        // Set all parameters
        for (int i = 0; i < tableTags.size(); i++) {
            TableTagEntity tag = tableTags.get(i);
            query.setParameter("blockId" + i, tag.getBlockId());
            query.setParameter("docId" + i, tag.getDocId());
            query.setParameter("embedding" + i, Utils.toVectorString(tag.getEmbedding()));
            query.setParameter("tag" + i, tag.getTag());
        }

        query.executeUpdate();
    }
}
