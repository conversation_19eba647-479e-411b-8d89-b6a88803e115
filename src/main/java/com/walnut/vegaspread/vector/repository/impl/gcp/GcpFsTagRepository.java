package com.walnut.vegaspread.vector.repository.impl.gcp;

import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;
import com.walnut.vegaspread.vector.model.FsTagMappingDto;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import com.walnut.vegaspread.vector.service.Utils;
import io.quarkus.arc.profile.UnlessBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.persistence.Query;
import jakarta.transaction.Transactional;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
@UnlessBuildProfile("aws")
public class GcpFsTagRepository implements FsTagRepositoryInterface {
    private final EntityManager em;

    @Inject
    public GcpFsTagRepository(EntityManager em) {
        this.em = em;
    }

    @Override
    public List<FsTagEntity> findSimilar(FsTagDto.Query queryDto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("SELECT ")
                .append(FsTagEntity.BLOCK_ID_COL_NAME).append(" AS blockId")
                .append(", ")
                .append(FsTagEntity.DOC_ID_COL_NAME).append(" AS docId")
                .append(", ")
                .append(" vector_to_string(")
                .append(FsTagEntity.EMBEDDING_COL_NAME)
                .append(") as embedding")
                .append(", ")
                .append(FsTagEntity.TAG_COL_NAME)
                .append(", ")
                .append(FsTagEntity.ENTITY_ID_COL_NAME).append(" AS entityId")
                .append(", ")
                .append(FsTagEntity.ENTITY_NAME_COL_NAME).append(" AS entityName")
                .append(", ")
                .append(FsTagEntity.SPREAD_LEVEL_COL_NAME).append(" AS spreadLevel")
                .append(", ")
                .append(FsTagEntity.LAST_MODIFIED_TIME_COL_NAME).append(" AS lastModifiedTime")
                .append(" FROM ")
                .append(FsTagEntity.TABLE_NAME)
                .append(" WHERE ")
                .append(FsTagEntity.ENTITY_ID_COL_NAME)
                .append(" = :entityId")
                .append(" AND (1 - cosine_distance(")
                .append(FsTagEntity.EMBEDDING_COL_NAME)
                .append(", string_to_vector(:queryVector))) > :scoreThreshold")
                .append(" ORDER BY cosine_distance(")
                .append(FsTagEntity.EMBEDDING_COL_NAME)
                .append(", string_to_vector(:queryVector))")
                .append(" LIMIT :count");

        Query query = em.createNativeQuery(queryBuilder.toString(), "FsTagDtoMapping");

        List<FsTagMappingDto> results = query.setParameter("entityId", queryDto.entityId())
                .setParameter("queryVector", Utils.toVectorString(queryDto.queryVector()))
                .setParameter("scoreThreshold", queryDto.scoreThreshold())
                .setParameter("count", queryDto.count())
                .getResultList();
        return results.stream().map(result -> new FsTagEntity(result.blockId(), result.docId(),
                        Utils.fromVectorString(result.embedding()), result.tag(), result.entityId(),
                        result.entityName(),
                        result.spreadLevel(), result.lastModifiedTime()))
                .toList();
    }

    @Override
    @Transactional
    public void persist(List<FsTagEntity> fsTags) {
        if (fsTags == null || fsTags.isEmpty()) {
            return;
        }

        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("INSERT INTO ")
                .append(FsTagEntity.TABLE_NAME)
                .append(" (block_id, doc_id, last_modified_time, embedding, tag, entity_id, entity_name, " +
                        "spread_level) VALUES ");

        // Build the VALUES part
        for (int i = 0; i < fsTags.size(); i++) {
            queryBuilder.append("(")
                    .append(":blockId").append(i).append(", ")
                    .append("UNHEX(REPLACE(:docId").append(i).append(", '-', '')), ")
                    .append("NOW(), ")
                    .append("string_to_vector(:embedding").append(i).append("), ")
                    .append(":tag").append(i).append(", ")
                    .append(":entityId").append(i).append(", ")
                    .append(":entityName").append(i).append(", ")
                    .append(":spreadLevel").append(i).append(")");

            if (i < fsTags.size() - 1) {
                queryBuilder.append(", ");
            }
        }

        Query query = em.createNativeQuery(queryBuilder.toString());

        // Set all parameters
        for (int i = 0; i < fsTags.size(); i++) {
            FsTagEntity tag = fsTags.get(i);
            query.setParameter("blockId" + i, tag.getBlockId());
            query.setParameter("docId" + i, tag.getDocId().toString());
            query.setParameter("embedding" + i, Utils.toVectorString(tag.getEmbedding()));
            query.setParameter("tag" + i, tag.getTag());
            query.setParameter("entityId" + i, tag.getEntityId());
            query.setParameter("entityName" + i, tag.getEntityName());
            query.setParameter("spreadLevel" + i, tag.getSpreadLevel());
        }

        query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByDocId(UUID docId) {
        String queryBuilder = String.format("DELETE FROM %s WHERE doc_id = UNHEX(REPLACE(:docId, '-', ''))",
                FsTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("docId", docId.toString());
        return query.executeUpdate();
    }

    @Override
    @Transactional
    public long deleteByBlockId(Integer blockId) {
        String queryBuilder = String.format("DELETE FROM %s WHERE block_id = :blockId", FsTagEntity.TABLE_NAME);
        Query query = em.createNativeQuery(queryBuilder);
        query.setParameter("blockId", blockId);
        return query.executeUpdate();
    }
}
