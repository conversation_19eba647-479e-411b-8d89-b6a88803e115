package com.walnut.vegaspread.vector.service;

import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;
import com.walnut.vegaspread.vector.repository.FsTagRepositoryFactory;
import com.walnut.vegaspread.vector.repository.spi.FsTagRepositoryInterface;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ApplicationScoped
public class FsTagService {
    private final FsTagRepositoryInterface fsTagRepository;

    @Inject
    public FsTagService(FsTagRepositoryFactory fsTagRepositoryFactory) {
        this.fsTagRepository = fsTagRepositoryFactory.getFsTagRepository();
    }

    @Transactional
    public List<FsTagEntity> createFsTag(List<FsTagDto.Create> createDtos) {
        if (createDtos == null || createDtos.isEmpty()) {
            return Collections.emptyList();
        }
        List<FsTagEntity> fsTables = new ArrayList<>();
        for (FsTagDto.Create createDto : createDtos) {
            FsTagEntity fsTable = new FsTagEntity();
            fsTable.setBlockId(createDto.blockId());
            fsTable.setDocId(createDto.docId());
            fsTable.setTag(createDto.tag());
            fsTable.setEntityId(createDto.entityId());
            fsTable.setEntityName(createDto.entityName());
            fsTable.setSpreadLevel(createDto.spreadLevel());
            fsTable.setEmbedding(Utils.toFloatArray(createDto.embedding()));
            fsTable.setLastModifiedTime(LocalDateTime.now());
            fsTables.add(fsTable);
        }
        fsTagRepository.persist(fsTables);
        return fsTables;
    }

    public List<FsTagEntity> queryFsTag(FsTagDto.Query queryDto) {
        if (queryDto == null) {
            return Collections.emptyList();
        }
        return fsTagRepository.findSimilar(queryDto);
    }

    @Transactional
    public long deleteFsTagDoc(FsTagDto.DeleteDoc deleteDocDto) {
        return fsTagRepository.deleteByDocId(deleteDocDto.docId());
    }

    @Transactional
    public long deleteFsTagBlock(FsTagDto.DeleteBlock deleteBlockDto) {
        return fsTagRepository.deleteByBlockId(deleteBlockDto.blockId());
    }
}
