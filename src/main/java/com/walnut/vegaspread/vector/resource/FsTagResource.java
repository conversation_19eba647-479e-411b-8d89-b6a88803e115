package com.walnut.vegaspread.vector.resource;

import com.walnut.vegaspread.common.model.StatusDto;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.vector.entity.FsTagEntity;
import com.walnut.vegaspread.vector.model.FsTagDto;
import com.walnut.vegaspread.vector.service.FsTagService;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/wise/processor/fs-tag")
@ApiKeyAuthenticate
public class FsTagResource {

    private final FsTagService fsTagService;

    @Inject
    public FsTagResource(FsTagService fsTagService) {
        this.fsTagService = fsTagService;
    }

    @POST
    public StatusDto createFsTag(List<FsTagDto.Create> createDtos) {
        fsTagService.createFsTag(createDtos);
        return new StatusDto("Fs tag vector saved successfully");
    }

    @POST
    @Path("/query")
    public FsTagDto.QueryResponse queryFsTag(FsTagDto.Query queryDtos) {
        List<FsTagEntity> fsTagEntities = fsTagService.queryFsTag(queryDtos);
        return new FsTagDto.QueryResponse(fsTagEntities.stream()
                .map(fsTagEntity -> new FsTagDto.QueryResponseItem(fsTagEntity.getBlockId(), fsTagEntity.getTag(),
                        fsTagEntity.getEntityId(),
                        fsTagEntity.getEntityName(), fsTagEntity.getSpreadLevel()))
                .toList());
    }

    @DELETE
    @Path("/document")
    public long deleteFsTagDoc(FsTagDto.DeleteDoc deleteDocDto) {
        return fsTagService.deleteFsTagDoc(deleteDocDto);
    }

    @DELETE
    @Path("/block")
    public long deleteFsTagBlock(FsTagDto.DeleteBlock deleteBlockDto) {
        return fsTagService.deleteFsTagBlock(deleteBlockDto);
    }
}
