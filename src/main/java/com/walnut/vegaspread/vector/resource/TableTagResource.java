package com.walnut.vegaspread.vector.resource;

import com.walnut.vegaspread.common.model.StatusDto;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.vector.model.TableTagDto;
import com.walnut.vegaspread.vector.service.TableTagService;
import jakarta.inject.Inject;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Info;

import java.util.List;

@OpenAPIDefinition(info = @Info(title = "Processor API authenticated by X-API-Key", version = "1.0"))
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/wise/processor/table-tag")
@ApiKeyAuthenticate
public class TableTagResource {

    private final TableTagService tableTagService;

    @Inject
    public TableTagResource(TableTagService tableTagService) {
        this.tableTagService = tableTagService;
    }

    @POST
    public StatusDto createTableTag(List<TableTagDto.Create> createDtos) {
        tableTagService.createTableTag(createDtos);
        return new StatusDto("Table tag vector saved successfully");
    }

    @POST
    @Path("/query")
    public TableTagDto.QueryResponse queryTableTag(TableTagDto.Query queryDtos) {
        return tableTagService.queryTableTag(queryDtos);
    }

    @DELETE
    @Path("/document")
    public long deleteTableTagDoc(TableTagDto.DeleteDoc deleteDocDto) {
        return tableTagService.deleteTableTagDoc(deleteDocDto);
    }

    @DELETE
    @Path("/block")
    public long deleteTableTagBlock(TableTagDto.DeleteBlock deleteBlockDto) {
        return tableTagService.deleteTableTagBlock(deleteBlockDto);
    }
}
