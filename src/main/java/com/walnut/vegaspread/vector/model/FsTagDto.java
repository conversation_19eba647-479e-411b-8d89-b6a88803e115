package com.walnut.vegaspread.vector.model;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public interface FsTagDto {
    record Create(Integer blockId, @NotNull UUID docId, @NotNull @Size(max = 255) String tag,
                  @NotNull Integer entityId, @NotNull @Size(max = 255) String entityName,
                  @NotNull @Size(max = 255) String spreadLevel, ArrayList<Float> embedding) implements Serializable {
    }

    record Query(@NotNull Integer entityId, ArrayList<Float> queryVector, @NotNull Integer count,
                 Double scoreThreshold) {
    }

    record QueryResponse(List<QueryResponseItem> items) implements Serializable {

    }

    record QueryResponseItem(Integer blockId, String tag, Integer entityId, String entityName,
                             String spreadLevel) implements Serializable {

    }

    record DeleteDoc(@NotNull UUID docId) {
    }

    record DeleteBlock(@NotNull Integer blockId) {
    }
}