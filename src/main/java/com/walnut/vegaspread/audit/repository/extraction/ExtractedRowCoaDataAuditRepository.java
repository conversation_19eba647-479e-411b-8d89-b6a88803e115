package com.walnut.vegaspread.audit.repository.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedRowCoaDataAuditEntity;
import com.walnut.vegaspread.audit.model.common.JpaReflection;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Parameters;
import jakarta.enterprise.context.ApplicationScoped;

import java.time.LocalDate;
import java.util.List;

@ApplicationScoped
public class ExtractedRowCoaDataAuditRepository implements PanacheRepositoryBase<ExtractedRowCoaDataAuditEntity,
        Integer> {

    public List<String> distinctUsersForFilteredFieldInTables(StringBuilder query, Parameters params) {
        query.insert(0, "SELECT DISTINCT auditedBy FROM ExtractedRowCoaDataAuditEntity WHERE ");
        return find(query.toString(), params).project(String.class).list();
    }

    public List<LocalDate> distinctAuditDatesForFilteredFieldInTables(StringBuilder query, Parameters params) {
        query.insert(0, "SELECT DISTINCT DATE(auditTime) FROM ExtractedRowCoaDataAuditEntity WHERE ");
        return find(query.toString(), params).project(JpaReflection.AuditDateDto.class).stream().map(
                JpaReflection.AuditDateDto::getAuditDate).toList();
    }

    public List<ExtractedRowCoaDataAuditEntity> findByTableIdsAndColName(List<Integer> tableIds, String colName) {
        return find("tableId in ?1 AND colName = ?2", tableIds, colName).list();
    }
}
