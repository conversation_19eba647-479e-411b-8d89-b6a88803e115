package com.walnut.vegaspread.audit.service.common;

import com.walnut.vegaspread.audit.model.common.AuditListType;
import com.walnut.vegaspread.audit.model.common.HistoryDto;
import com.walnut.vegaspread.audit.service.extraction.ExtractedRowCoaDataAuditService;
import com.walnut.vegaspread.audit.service.extraction.ExtractedTableRowAuditService;
import com.walnut.vegaspread.audit.service.extraction.LayoutBlockAuditService;
import jakarta.enterprise.context.ApplicationScoped;

import java.security.InvalidParameterException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

@ApplicationScoped
public class HistoryService {

    private final ExtractedTableRowAuditService extractedTableRowAuditService;
    private final LayoutBlockAuditService layoutBlockAuditService;
    private final ExchangeService exchangeService;
    private final ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService;

    public HistoryService(
            ExtractedTableRowAuditService extractedTableRowAuditService,
            LayoutBlockAuditService layoutBlockAuditService,
            ExchangeService exchangeService,
            ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService) {
        this.extractedTableRowAuditService = extractedTableRowAuditService;
        this.layoutBlockAuditService = layoutBlockAuditService;

        this.exchangeService = exchangeService;
        this.extractedRowCoaDataAuditService = extractedRowCoaDataAuditService;
    }

    public HistoryDto.Response getHistory(HistoryDto.Request auditListDto) {

        switch (auditListDto.auditType()) {
            case COA_ID -> {
                HistoryDto.Response coaHistory = extractedRowCoaDataAuditService.getHistory(auditListDto, "coa_id");

                //Get a list of coa ids from prev value and new value in audit.
                List<Integer> auditCoaIds = coaHistory.auditItems()
                        .stream()
                        .filter(auditItem -> !auditItem.prevValue().equals("NA") || !auditItem.newValue().equals("NA"))
                        .flatMap(auditItem -> Stream.of(Integer.valueOf(auditItem.prevValue()),
                                Integer.valueOf(auditItem.newValue())))
                        .distinct()
                        .toList();

                //Create a map of coa id and coa text.
                Map<String, String> coaIdCoaTextMap = new HashMap<>();
                exchangeService.getCoas(auditCoaIds)
                        .forEach(
                                coaItemDto -> coaIdCoaTextMap.put(coaItemDto.coaId().toString(), coaItemDto.coaText()));

                //Replace prev coa id and new coa id with coa text for ids.
                return new HistoryDto.Response(
                        coaHistory.pageNumber(),
                        coaHistory.pageSize(),
                        coaHistory.totalPages(),
                        coaHistory.auditItems()
                                .stream()
                                .map(auditItem -> new HistoryDto.Item(
                                        auditItem.blockId(),
                                        auditItem.rowId(),
                                        coaIdCoaTextMap.get(auditItem.prevValue()),
                                        coaIdCoaTextMap.get(auditItem.newValue()),
                                        auditItem.action(),
                                        auditItem.auditTime(),
                                        auditItem.auditedBy(),
                                        auditItem.auditedBYFullName()))
                                .toList(),
                        coaHistory.allUsers(),
                        coaHistory.allDates());
            }
            case HEADER_ID -> {
                return extractedTableRowAuditService.getHistory(auditListDto, "header_ids");
            }
            case TAG -> {
                return layoutBlockAuditService.getHistory(auditListDto, "tag");
            }
            default -> throw new InvalidParameterException("Invalid audit type: " + auditListDto.auditType());
        }
    }

    public List<String> getAuditTypes() {
        return Stream.of(AuditListType.values()).map(Enum::name).toList();
    }
}
