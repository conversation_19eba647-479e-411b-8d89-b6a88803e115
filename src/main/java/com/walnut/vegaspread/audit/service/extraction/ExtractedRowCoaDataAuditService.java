package com.walnut.vegaspread.audit.service.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedRowCoaDataAuditEntity;
import com.walnut.vegaspread.audit.model.common.HistoryDto;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedRowCoaDataAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.audit.service.view.SpreadAuditedBlockMappingService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.panache.common.Parameters;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.jboss.logging.Logger;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@ApplicationScoped
public class ExtractedRowCoaDataAuditService {

    private static final Logger logger = Logger.getLogger(ExtractedRowCoaDataAuditService.class);
    private static final Sort DEFAULT_SORT = Sort.by("auditTime", Sort.Direction.Descending);
    private final JsonWebToken accessToken;
    private final ExtractedRowCoaDataAuditRepository extractedRowCoaDataAuditRepository;
    private final SpreadAuditedBlockMappingService spreadAuditedBlockMappingService;
    private final ExchangeService exchangeService;

    public ExtractedRowCoaDataAuditService(JsonWebToken accessToken,
                                           ExtractedRowCoaDataAuditRepository extractedRowCoaDataAuditRepository,
                                           SpreadAuditedBlockMappingService spreadAuditedBlockMappingService,
                                           ExchangeService exchangeService) {
        this.accessToken = accessToken;
        this.extractedRowCoaDataAuditRepository = extractedRowCoaDataAuditRepository;
        this.spreadAuditedBlockMappingService = spreadAuditedBlockMappingService;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<ExtractedRowCoaDataAuditDto.Response> auditForCreate(
            List<ExtractedRowCoaDataAuditDto.Create> extractedRowCoaDataCreateAuditDtos, String username) {
        logger.debugf("auditForCreate - Starting with %s audit entries, username: %s",
                (extractedRowCoaDataCreateAuditDtos != null ? extractedRowCoaDataCreateAuditDtos.size() : "null"),
                username);

        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = new ArrayList<>();
        if (extractedRowCoaDataCreateAuditDtos == null || extractedRowCoaDataCreateAuditDtos.isEmpty()) {
            logger.debugf("auditForCreate - No audit entries to process, returning empty list");
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }

            logger.debugf("auditForCreate - Processing %d audit entries", extractedRowCoaDataCreateAuditDtos.size());
            for (ExtractedRowCoaDataAuditDto.Create extractedRowCoaDataCreateAuditDto :
                    extractedRowCoaDataCreateAuditDtos) {
                logger.debugf("auditForCreate - Processing entry: %s", extractedRowCoaDataCreateAuditDto);

                ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = new ExtractedRowCoaDataAuditEntity();
                extractedRowCoaDataAuditEntity.setTableId(extractedRowCoaDataCreateAuditDto.tableId())
                        .setRowId(extractedRowCoaDataCreateAuditDto.rowId().shortValue())
                        .setColName(extractedRowCoaDataCreateAuditDto.colName())
                        .setPrevValue("coa_id".equals(extractedRowCoaDataCreateAuditDto.colName()) ? "1" : null)
                        .setNewValue(extractedRowCoaDataCreateAuditDto.newValue())
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity);

                logger.debugf("auditForCreate - Created audit entity: %s", extractedRowCoaDataAuditEntity);
            }

            logger.debugf("auditForCreate - Persisting %d audit entities", extractedRowCoaDataAuditEntities.size());
            extractedRowCoaDataAuditRepository.persist(extractedRowCoaDataAuditEntities);

            List<ExtractedRowCoaDataAuditDto.Response> responses = ExtractedRowCoaDataAuditEntity.toDtoList(
                    extractedRowCoaDataAuditEntities);
            logger.debugf("auditForCreate - Returning %d response DTOs", responses.size());
            return responses;
        }
    }

    @Transactional
    public List<ExtractedRowCoaDataAuditDto.Response> auditForUpdate(
            List<ExtractedRowCoaDataAuditDto.Update> extractedRowCoaDataUpdateAuditDtos, String username) {
        logger.debugf("auditForUpdate - Starting with %s audit entries, username: %s",
                (extractedRowCoaDataUpdateAuditDtos != null ? extractedRowCoaDataUpdateAuditDtos.size() : "null"),
                username);

        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = new ArrayList<>();
        if (extractedRowCoaDataUpdateAuditDtos == null || extractedRowCoaDataUpdateAuditDtos.isEmpty()) {
            logger.debugf("auditForUpdate - No audit entries to process, returning empty list");
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }

            logger.debugf("auditForUpdate - Processing %d audit entries", extractedRowCoaDataUpdateAuditDtos.size());
            for (ExtractedRowCoaDataAuditDto.Update extractedRowCoaDataUpdateAuditDto :
                    extractedRowCoaDataUpdateAuditDtos) {
                logger.debugf("auditForUpdate - Processing entry: %s", extractedRowCoaDataUpdateAuditDto);

                ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = new ExtractedRowCoaDataAuditEntity();
                extractedRowCoaDataAuditEntity.setTableId(extractedRowCoaDataUpdateAuditDto.tableId())
                        .setRowId(extractedRowCoaDataUpdateAuditDto.rowId().shortValue())
                        .setColName(extractedRowCoaDataUpdateAuditDto.colName())
                        .setPrevValue(extractedRowCoaDataUpdateAuditDto.prevValue())
                        .setNewValue(extractedRowCoaDataUpdateAuditDto.newValue())
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity);

                logger.debugf("auditForUpdate - Created audit entity: %s", extractedRowCoaDataAuditEntity);
            }

            logger.debugf("auditForUpdate - Persisting %d audit entities", extractedRowCoaDataAuditEntities.size());
            extractedRowCoaDataAuditRepository.persist(extractedRowCoaDataAuditEntities);

            List<ExtractedRowCoaDataAuditDto.Response> responses = ExtractedRowCoaDataAuditEntity.toDtoList(
                    extractedRowCoaDataAuditEntities);
            logger.debugf("auditForUpdate - Returning %d response DTOs", responses.size());
            return responses;
        }
    }

    @Transactional
    public List<ExtractedRowCoaDataAuditDto.Response> auditForDelete(
            List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDataDeleteAuditDtos, String username) {
        logger.debugf("auditForDelete - Starting with %s audit entries, username: %s",
                (extractedRowCoaDataDeleteAuditDtos != null ? extractedRowCoaDataDeleteAuditDtos.size() : "null"),
                username);

        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = new ArrayList<>();
        if (extractedRowCoaDataDeleteAuditDtos == null || extractedRowCoaDataDeleteAuditDtos.isEmpty()) {
            logger.debugf("auditForDelete - No audit entries to process, returning empty list");
            return Collections.emptyList();
        } else {
            String fullName = username;
            if (username == null) {
                username = getUsername(accessToken);
                fullName = exchangeService.getNameFromUsername(username);
            }

            logger.debugf("auditForDelete - Processing %d audit entries", extractedRowCoaDataDeleteAuditDtos.size());
            for (ExtractedRowCoaDataAuditDto.Delete extractedRowCoaDataDeleteAuditDto :
                    extractedRowCoaDataDeleteAuditDtos) {
                logger.debugf("auditForDelete - Processing entry: %s", extractedRowCoaDataDeleteAuditDto);

                ExtractedRowCoaDataAuditEntity extractedRowCoaDataAuditEntity = new ExtractedRowCoaDataAuditEntity();
                extractedRowCoaDataAuditEntity.setTableId(extractedRowCoaDataDeleteAuditDto.tableId())
                        .setRowId(extractedRowCoaDataDeleteAuditDto.rowId().shortValue())
                        .setColName(extractedRowCoaDataDeleteAuditDto.colName())
                        .setPrevValue(extractedRowCoaDataDeleteAuditDto.prevValue())
                        .setNewValue("coa_id".equals(extractedRowCoaDataDeleteAuditDto.colName()) ? "1" : null)
                        .setAction(AuditStatus.DELETED)
                        .setAuditedBy(username)
                        .setAuditedBYFullName(fullName)
                        .setAuditTime(LocalDateTime.now());
                extractedRowCoaDataAuditEntities.add(extractedRowCoaDataAuditEntity);

                logger.debugf("auditForDelete - Created audit entity: %s", extractedRowCoaDataAuditEntity);
            }

            logger.debugf("auditForDelete - Persisting %d audit entities", extractedRowCoaDataAuditEntities.size());
            extractedRowCoaDataAuditRepository.persist(extractedRowCoaDataAuditEntities);

            List<ExtractedRowCoaDataAuditDto.Response> responses = ExtractedRowCoaDataAuditEntity.toDtoList(
                    extractedRowCoaDataAuditEntities);
            logger.debugf("auditForDelete - Returning %d response DTOs", responses.size());
            return responses;
        }
    }

    @Transactional
    public ExtractedRowCoaDataAuditDto.ListResponse filterAuditsForTables(
            ExtractedRowCoaDataAuditDto.ListForTables listTablesDto) {

        StringBuilder baseQuery = new StringBuilder("tableId IN :tableId AND colName = :colName");
        Parameters baseParams = Parameters.with("tableId", listTablesDto.tableIds());
        baseParams.and("colName", listTablesDto.colName());

        StringBuilder filterQuery = new StringBuilder(baseQuery);
        Parameters params = new Parameters();
        baseParams.map().forEach(params::and);

        if (listTablesDto.filterUser() != null) {
            filterQuery.append(" AND auditedBy = :auditedBy");
            params.and("auditedBy", listTablesDto.filterUser());
        }
        if (listTablesDto.filterDate() != null) {
            filterQuery.append(" AND DATE(auditTime) = :auditDate");
            params.and("auditDate", listTablesDto.filterDate());
        }

        //Get filtered,sorted and paginated records
        List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities = extractedRowCoaDataAuditRepository.find(
                        filterQuery.toString(), DEFAULT_SORT, params)
                .page(listTablesDto.pageNumber() - 1, listTablesDto.pageSize())
                .list();
        //Map full name for auditors.
        Map<String, String> nameMappings = exchangeService.getNamesFromUsernames(
                extractedRowCoaDataAuditEntities.stream()
                        .map(ExtractedRowCoaDataAuditEntity::getAuditedBy)
                        .distinct()
                        .toList());
        extractedRowCoaDataAuditEntities.forEach(extractedRowCoaDataAuditEntity ->
                extractedRowCoaDataAuditEntity.setAuditedBYFullName(
                        nameMappings.get(extractedRowCoaDataAuditEntity.getAuditedBy()))
        );

        long totalAudits = extractedRowCoaDataAuditRepository.count(filterQuery.toString(), params);
        int totalPages = (int) Math.ceil((double) totalAudits / listTablesDto.pageSize());

        List<String> allUsers = extractedRowCoaDataAuditRepository.distinctUsersForFilteredFieldInTables(
                new StringBuilder(baseQuery), baseParams);
        Map<String, String> allUsersMap = exchangeService.getNamesFromUsernames(allUsers);

        List<LocalDate> allDates = extractedRowCoaDataAuditRepository.distinctAuditDatesForFilteredFieldInTables(
                baseQuery, baseParams);

        return new ExtractedRowCoaDataAuditDto.ListResponse(listTablesDto.pageNumber(), listTablesDto.pageSize(),
                totalPages, ExtractedRowCoaDataAuditEntity.toDtoList(extractedRowCoaDataAuditEntities), allUsersMap,
                allDates);
    }

    public HistoryDto.Response getHistory(HistoryDto.Request auditListDto, String colName) {
        List<Integer> blockIds = spreadAuditedBlockMappingService.getAuditedBlockIdsForSpread(auditListDto.spreadId());

        ExtractedRowCoaDataAuditDto.ListResponse filteredAudits = filterAuditsForTables(
                new ExtractedRowCoaDataAuditDto.ListForTables(blockIds, colName, auditListDto.pageNumber(),
                        auditListDto.pageSize(), auditListDto.auditor(), auditListDto.auditDate()));

        return toHistory(filteredAudits);
    }

    public List<ExtractedRowCoaDataAuditDto.Response> getColAuditsForTables(List<Integer> tableIds, String colName) {
        if (tableIds == null || tableIds.isEmpty()) {
            return Collections.emptyList();
        }
        return ExtractedRowCoaDataAuditEntity.toDtoList(
                extractedRowCoaDataAuditRepository.findByTableIdsAndColName(tableIds, colName));
    }

    private HistoryDto.Response toHistory(ExtractedRowCoaDataAuditDto.ListResponse filteredAudits) {
        return new HistoryDto.Response(
                filteredAudits.pageNumber(),
                filteredAudits.pageSize(),
                filteredAudits.totalPages(),
                filteredAudits.extractedRowCoaDataAudits().stream().map(filteredAudit -> new HistoryDto.Item(
                        filteredAudit.tableId(),
                        filteredAudit.rowId(),
                        filteredAudit.prevValue() == null ? "NA" : filteredAudit.prevValue(),
                        filteredAudit.newValue() == null ? "NA" : filteredAudit.newValue(),
                        filteredAudit.action(),
                        filteredAudit.auditTime(),
                        filteredAudit.auditedBy(),
                        filteredAudit.auditedBYFullName() == null ? "NA" : filteredAudit.auditedBYFullName())).toList(),
                filteredAudits.allUsers(),
                filteredAudits.allDates()
        );
    }
}