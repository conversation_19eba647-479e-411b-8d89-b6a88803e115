package com.walnut.vegaspread.audit.model.common;

import io.quarkus.runtime.annotations.RegisterForReflection;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;

public interface JpaReflection {
    @Getter
    @RegisterForReflection
    class CoaIdDto {
        Long coaId;

        public CoaIdDto(Long coaId) {

            this.coaId = coaId;
        }
    }

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    class BlockIdDto {
        Integer blockId;
    }

    @Getter
    @RegisterForReflection
    class AuditDateDto {
        public final LocalDate auditDate;

        public AuditDateDto(Object date) {
            if (date instanceof java.sql.Date) {
                this.auditDate = ((java.sql.Date) date).toLocalDate();
            } else if (date instanceof java.time.LocalDate) {
                this.auditDate = (LocalDate) date; // Handle LocalDate directly
            } else {
                throw new IllegalArgumentException("Unsupported date type: " + date.getClass());
            }
        }
    }
}