package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.service.extraction.ExtractedRowCoaDataAuditService;
import com.walnut.vegaspread.audit.service.extraction.ExtractedTableRowAuditService;
import com.walnut.vegaspread.audit.service.extraction.LayoutBlockAuditService;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("api-auth")
@ApiKeyAuthenticate
public class ApiAuthResource {
    public static final String ROW_PREFIX = "/extracted/table/row";
    public static final String BLOCK_PREFIX = "/extracted/block";
    public static final String API_USER = "developer";
    public static final String ROW_COA_DATA_JOIN_PREFIX = "/extracted/table/row-coa";

    private final ExtractedTableRowAuditService extractedTableRowAuditService;
    private final LayoutBlockAuditService layoutBlockAuditService;
    private final ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService;

    public ApiAuthResource(ExtractedTableRowAuditService extractedTableRowAuditService,
                           LayoutBlockAuditService layoutBlockAuditService,
                           ExtractedRowCoaDataAuditService extractedRowCoaDataAuditService) {
        this.extractedTableRowAuditService = extractedTableRowAuditService;
        this.layoutBlockAuditService = layoutBlockAuditService;
        this.extractedRowCoaDataAuditService = extractedRowCoaDataAuditService;
    }

    @POST
    @Path(ROW_PREFIX + "/audit-update")
    public List<ExtractedTableRowAuditDto.Response> auditRowsForUpdate(
            List<ExtractedTableRowAuditDto.Update> extractedTableRowUpdateAuditDtos) {
        return extractedTableRowAuditService.auditForUpdate(extractedTableRowUpdateAuditDtos, API_USER);
    }

    @POST
    @Path(BLOCK_PREFIX + "/audit-create")
    public List<LayoutBlockAuditDto.Response> auditBlocksForCreate(
            List<LayoutBlockAuditDto.Create> layoutBlockAuditCreateDtos) {
        return layoutBlockAuditService.auditForCreate(layoutBlockAuditCreateDtos, API_USER);
    }

    @POST
    @Path(BLOCK_PREFIX + "/audit-update")
    public List<LayoutBlockAuditDto.Response> auditBlocksForUpdate(
            List<LayoutBlockAuditDto.Update> layoutBlockAuditUpdateDtos) {
        return layoutBlockAuditService.auditForUpdate(layoutBlockAuditUpdateDtos, API_USER);
    }

    @POST
    @Path(BLOCK_PREFIX + "/audit-delete")
    public List<LayoutBlockAuditDto.Response> auditBlocksForDelete(
            List<LayoutBlockAuditDto.Delete> layoutBlockAuditDeleteDtos) {
        return layoutBlockAuditService.auditForDelete(layoutBlockAuditDeleteDtos, API_USER);
    }

    @POST
    @Path(ROW_COA_DATA_JOIN_PREFIX + "/audit-create")
    public List<ExtractedRowCoaDataAuditDto.Response> auditForCreate(
            List<ExtractedRowCoaDataAuditDto.Create> extractedRowCoaDataCreateAuditDtos) {
        return extractedRowCoaDataAuditService.auditForCreate(extractedRowCoaDataCreateAuditDtos, API_USER);
    }

    @POST
    @Path(ROW_COA_DATA_JOIN_PREFIX + "/audit-update")
    public List<ExtractedRowCoaDataAuditDto.Response> auditForUpdate(
            List<ExtractedRowCoaDataAuditDto.Update> extractedRowCoaDataUpdateAuditDtos) {
        return extractedRowCoaDataAuditService.auditForUpdate(extractedRowCoaDataUpdateAuditDtos, API_USER);
    }

    @POST
    @Path(ROW_COA_DATA_JOIN_PREFIX + "/audit-delete")
    public List<ExtractedRowCoaDataAuditDto.Response> auditForDelete(
            List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDataDeleteAuditDtos) {
        return extractedRowCoaDataAuditService.auditForDelete(extractedRowCoaDataDeleteAuditDtos, API_USER);
    }
}
