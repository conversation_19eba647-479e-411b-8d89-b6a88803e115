package com.walnut.vegaspread.audit.resource.workflow;

import com.walnut.vegaspread.audit.service.workflow.DocumentAuditService;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import io.quarkus.security.Authenticated;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/workflow/document")
@Authenticated
public class DocumentAuditResource {

    private final DocumentAuditService documentAuditService;

    public DocumentAuditResource(DocumentAuditService documentAuditService) {
        this.documentAuditService = documentAuditService;
    }

    @POST
    @Path("/audit-create")
    public List<DocumentAuditDto.Response> auditForCreate(List<DocumentAuditDto.Create> documentAuditCreateDtos) {
        return documentAuditService.auditForCreate(documentAuditCreateDtos);
    }

    @POST
    @Path("/audit-update")
    public List<DocumentAuditDto.Response> auditForUpdate(
            List<DocumentAuditDto.UpdateOrDelete> documentAuditUpdateDtos) {
        return documentAuditService.auditForUpdate(documentAuditUpdateDtos);
    }

    @POST
    @Path("/audit-delete")
    public List<DocumentAuditDto.Response> auditForDelete(
            List<DocumentAuditDto.UpdateOrDelete> documentAuditDeleteDtos) {
        return documentAuditService.auditForDelete(documentAuditDeleteDtos);
    }

    @GET
    @Path("/audit/{auditId}")
    public DocumentAuditDto.Response get(@PathParam("auditId") Integer id) {
        return documentAuditService.get(id);
    }

    @GET
    @Path("{docId}")
    public List<DocumentAuditDto.Response> getByDocId(@PathParam("docId") UUID docId) {
        return documentAuditService.getByDocId(docId);
    }
}
