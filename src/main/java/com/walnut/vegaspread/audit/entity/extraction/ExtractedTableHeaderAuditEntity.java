package com.walnut.vegaspread.audit.entity.extraction;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableHeaderAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@Accessors(chain = true)
@Entity
@Table(name = ExtractedTableHeaderAuditEntity.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
public class ExtractedTableHeaderAuditEntity extends BaseAuditEntity {
    public static final String TABLE_NAME = "extracted_table_header_audit";

    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String HEADER_ID_COL_NAME = "header_id";

    @Column(name = TABLE_ID_COL_NAME, nullable = false)
    private Integer tableId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = HEADER_ID_COL_NAME, nullable = false)
    private Integer headerId;

    public static List<ExtractedTableHeaderAuditDto.Response> toDtoList(
            List<ExtractedTableHeaderAuditEntity> extractedTableHeaderAuditEntities) {
        return extractedTableHeaderAuditEntities.stream()
                .map(ExtractedTableHeaderAuditEntity::toDto)
                .toList();
    }

    public ExtractedTableHeaderAuditDto.Response toDto() {
        return new ExtractedTableHeaderAuditDto.Response(
                this.getId(),
                this.getTableId(),
                this.getHeaderId(),
                this.getColName(),
                this.getPrevValue(),
                this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName()
        );
    }
}
