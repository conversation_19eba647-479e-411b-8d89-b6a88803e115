package com.walnut.vegaspread.audit.entity.extraction;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

@Accessors(chain = true)
@Entity
@Table(name = ExtractedRowCoaDataAuditEntity.TABLE_NAME)
@Getter
@Setter
@NoArgsConstructor
public class ExtractedRowCoaDataAuditEntity extends BaseAuditEntity {
    public static final String TABLE_NAME = "extracted_row_coa_data_audit";

    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String ROW_ID_COL_NAME = "row_id";

    @Column(name = TABLE_ID_COL_NAME, nullable = false)
    private Integer tableId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = ROW_ID_COL_NAME, nullable = false)
    private Short rowId;

    public static List<ExtractedRowCoaDataAuditDto.Response> toDtoList(
            List<ExtractedRowCoaDataAuditEntity> extractedRowCoaDataAuditEntities) {
        return extractedRowCoaDataAuditEntities.stream()
                .map(ExtractedRowCoaDataAuditEntity::toDto)
                .toList();
    }

    public ExtractedRowCoaDataAuditDto.Response toDto() {
        return new ExtractedRowCoaDataAuditDto.Response(
                this.getId(),
                this.getTableId(),
                this.getRowId().intValue(),
                this.getColName(),
                this.getPrevValue(),
                this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName()
        );
    }

    @Override
    public String toString() {
        return "ExtractedRowCoaDataAuditEntity{" +
                "id=" + getId() +
                ", tableId=" + tableId +
                ", rowId=" + rowId +
                ", colName='" + getColName() + '\'' +
                ", prevValue='" + getPrevValue() + '\'' +
                ", newValue='" + getNewValue() + '\'' +
                ", action=" + getAction() +
                ", auditTime=" + getAuditTime() +
                ", auditedBy='" + getAuditedBy() + '\'' +
                ", auditedByFullName='" + getAuditedBYFullName() + '\'' +
                '}';
    }
}
