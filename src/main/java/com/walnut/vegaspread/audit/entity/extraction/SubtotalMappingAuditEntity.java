package com.walnut.vegaspread.audit.entity.extraction;

import com.walnut.vegaspread.audit.entity.common.BaseAuditEntity;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalMappingAuditDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.List;

import static com.walnut.vegaspread.audit.entity.extraction.SubtotalMappingAuditEntity.TABLE_NAME;

@Accessors(chain = true)
@Getter
@Setter
@Entity
@Table(name = TABLE_NAME)
public class SubtotalMappingAuditEntity extends BaseAuditEntity {

    public static final String TABLE_NAME = "subtotal_mapping_audit";

    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String ROW_ID_COL_NAME = "row_id";
    @NotNull
    @Column(name = TABLE_ID_COL_NAME, nullable = false)
    private Integer tableId;

    @NotNull
    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = ROW_ID_COL_NAME, nullable = false)
    private Short rowId;

    public static List<SubtotalMappingAuditDto.Response> toDtoList(
            List<SubtotalMappingAuditEntity> subtotalMappingAuditEntities) {
        return subtotalMappingAuditEntities.stream()
                .map(SubtotalMappingAuditEntity::toDto)
                .toList();
    }

    public SubtotalMappingAuditDto.Response toDto() {
        return new SubtotalMappingAuditDto.Response(
                this.getId(),
                this.getTableId(),
                this.getRowId().intValue(),
                this.getColName(),
                this.getPrevValue(),
                this.getNewValue(),
                this.getAction(),
                this.getAuditTime(),
                this.getAuditedBy(),
                this.getAuditedBYFullName()
        );
    }
}