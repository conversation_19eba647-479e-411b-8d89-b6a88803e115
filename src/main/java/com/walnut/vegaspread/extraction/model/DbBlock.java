package com.walnut.vegaspread.extraction.model;

import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.UUID;

public interface DbBlock {

    @Builder
    @Data
    class BlockDto {
        private UUID docId;
        private int blockId;
        private Integer pageNum;
        private BlockTypeEnum blockType;
        private Integer xMin;
        private Integer xMax;
        private Integer yMin;
        private Integer yMax;
        private Integer score;
        private String tag;
        private String comment;
        private Integer tagExplainabilityId;
    }

    record CreateBlockDto(int pageNum, @NotNull BlockTypeEnum blockType, int xMin, int xMax, int yMin, int yMax,
                          int score, String tag, String comment) {
    }

    record CreateTableBlockDto(int xMin, int xMax, int yMin, int yMax) {
    }

    record UpdateBlockTagDto(int blockId, @NotNull String tag) {
    }

    record UpdateBlockTagByProcessorDto(int blockId, @NotNull String tag, Integer tagExplainabilityId) {
    }

    record UpdateBlockCommentDto(int blockId, @NotNull String comment) {
    }

    record UpdateBlockBboxDto(int xMin, int xMax, int yMin, int yMax) {
    }

    @Getter
    @AllArgsConstructor
    @RegisterForReflection
    class BlockTagOnly {
        Integer blockId;
        String tag;
        Short pageNum;
    }
}
