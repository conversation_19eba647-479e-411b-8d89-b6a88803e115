package com.walnut.vegaspread.extraction.entity;

import com.walnut.vegaspread.extraction.model.MappedRowDto;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.UUID;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "coa_mapping")
public class CoaMappingEntity {

    public static final String DOC_ID_COL_NAME = "doc_id";
    public static final String TABLE_TYPE_COL_NAME = "table_type";
    public static final String ROW_PARENT_COL_NAME = "row_parent";
    public static final String TEXT_COL_NAME = "text";
    public static final String FS_HEADER_COL_NAME = "fs_header";
    public static final String FS_TEXT_COL_NAME = "fs_text";
    public static final String COA_ID_COL_NAME = "coa_id";
    public static final String IS_APPROVED_COL_NAME = "is_approved";
    public static final String TABLE_ID_COL_NAME = "table_id";
    public static final String ROW_ID_COL_NAME = "row_id";
    public static final String COA_MAPPING_ID_COL_NAME = "id";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = COA_MAPPING_ID_COL_NAME, nullable = false)
    private Integer id;

    @Column(name = TABLE_ID_COL_NAME, nullable = false)
    private Integer tableId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = ROW_ID_COL_NAME, nullable = false)
    private Short rowId;

    @Column(name = DOC_ID_COL_NAME, nullable = false)
    private UUID docId;

    @Column(name = TABLE_TYPE_COL_NAME, nullable = false)
    private String tableType;

    @Column(name = ROW_PARENT_COL_NAME, nullable = false)
    private String rowParent;

    @Column(name = TEXT_COL_NAME, nullable = false)
    private String text;

    @Column(name = FS_HEADER_COL_NAME, nullable = false)
    private String fsHeader;

    @Column(name = FS_TEXT_COL_NAME, nullable = false)
    private String fsText;

    @Column(name = COA_ID_COL_NAME, nullable = false)
    private Integer coaId;

    @Column(name = IS_APPROVED_COL_NAME, nullable = false)
    private Boolean isApproved;

    public CoaMappingEntity(CoaMappingEntity other) {
        this.id = other.id;
        this.tableId = other.tableId;
        this.rowId = other.rowId;
        this.docId = other.docId;
        this.tableType = other.tableType;
        this.rowParent = other.rowParent;
        this.text = other.text;
        this.fsHeader = other.fsHeader;
        this.fsText = other.fsText;
        this.coaId = other.coaId;
        this.isApproved = other.isApproved;
    }

    public static MappedRowDto toDto(CoaMappingEntity coaMappingEntity) {
        return new MappedRowDto(
                coaMappingEntity.getId(),
                coaMappingEntity.getTableId(),
                coaMappingEntity.getRowId().intValue(),
                coaMappingEntity.getDocId(),
                coaMappingEntity.getTableType(),
                coaMappingEntity.getRowParent(),
                coaMappingEntity.getText(),
                coaMappingEntity.getFsHeader(),
                coaMappingEntity.getFsText(),
                coaMappingEntity.getCoaId());
    }

    public MappedRowDto toDto() {
        return new MappedRowDto(id, tableId, Integer.valueOf(getRowId()), docId, tableType,
                rowParent, text, fsHeader, fsText, coaId);
    }
}
