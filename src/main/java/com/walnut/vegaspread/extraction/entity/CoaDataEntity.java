package com.walnut.vegaspread.extraction.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.eclipse.microprofile.openapi.annotations.enums.SchemaType;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

import java.util.Objects;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = CoaDataEntity.TABLE_NAME)
public class CoaDataEntity {

    public static final String TABLE_NAME = "coa_data";
    public static final String ID_COL_NAME = "id";
    public static final String COA_ID_COL_NAME = "coa_id";
    public static final String COA_SCORE_COL_NAME = "coa_score";
    public static final String USE_COA_COL_NAME = "use_coa";

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = ID_COL_NAME, nullable = false)
    public Integer id;

    @Column(name = COA_ID_COL_NAME)
    public Integer coaId;

    @Schema(type = SchemaType.INTEGER, example = "0")
    @Column(name = COA_SCORE_COL_NAME)
    public Byte coaScore;

    @Column(name = USE_COA_COL_NAME)
    public Boolean useCoa;

    public CoaDataEntity(CoaDataEntity other) {
        this.id = other.id;
        this.coaId = other.coaId;
        this.coaScore = other.coaScore;
        this.useCoa = other.useCoa;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof CoaDataEntity that)) return false;
        return Objects.equals(getId(), that.getId()) && Objects.equals(getCoaId(),
                that.getCoaId()) && Objects.equals(getCoaScore(), that.getCoaScore()) && Objects.equals(
                getUseCoa(), that.getUseCoa());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId(), getCoaId(), getCoaScore(), getUseCoa());
    }
}
