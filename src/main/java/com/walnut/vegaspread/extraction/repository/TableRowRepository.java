package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class TableRowRepository implements PanacheRepositoryBase<TableRowEntity, TableRowPkId> {

    public List<TableRowEntity> findAllByNtaBlockId(Integer tableId) {
        return list("ntaTable.blockId = ?1", tableId);
    }

    public long deleteForTableIds(List<Integer> tableIds) {
        return delete("tableRowPkId.tableId in ?1", tableIds);
    }

    public List<TableRowEntity> findByTableId(Integer tableId) {
        return find("tableRowPkId.tableId in ?1", tableId).list();
    }

    public List<TableRowEntity> getForTableSortByPos(Integer tableId) {
        return find("tableRowPkId.tableId = ?1", Sort.by("pos", Sort.Direction.Ascending), tableId).list();
    }

    public Short findMaxRowIdForTable(int tableId) {
        return find(" SELECT MAX(tableRowPkId.rowId) FROM TableRowEntity WHERE tableRowPkId.tableId = ?1",
                tableId).project(Short.class)
                .firstResult();
    }

    public long deleteForTableIdAndRowId(int tableId, int rowId) {
        return delete("tableRowPkId.tableId = ?1 AND tableRowPkId.rowId = ?2", tableId, rowId);
    }

    public List<TableRowEntity> findNtaLinkedRowsForTables(List<Integer> tableIds) {
        return list("tableRowPkId.tableId IN ?1 AND ntaTable IS NOT NULL", tableIds);
    }
}
