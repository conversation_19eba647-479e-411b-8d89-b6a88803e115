package com.walnut.vegaspread.extraction.repository;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class CoaMappingRepository implements PanacheRepositoryBase<CoaMappingEntity, Integer> {

    public List<CoaMappingEntity> findByDocIdsAndTableType(List<UUID> docIds, String tableType) {
        if (tableType.equals("all")) {
            return list("docId IN ?1", docIds);
        }
        return list("docId IN ?1 AND tableType = ?2", docIds, tableType);
    }

    public long deleteByDocIds(List<UUID> docIds) {
        return delete("docId IN ?1", docIds);
    }

    public List<CoaMappingEntity> findByIds(List<Integer> ids) {
        return list("id IN ?1", ids);
    }
}
