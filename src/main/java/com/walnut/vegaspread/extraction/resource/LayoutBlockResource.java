package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.service.LayoutService;
import com.walnut.vegaspread.extraction.service.mapper.LayoutBlockEntityMapper;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.jboss.resteasy.reactive.RestQuery;

import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/block")
@Authenticated
public class LayoutBlockResource {

    private final LayoutService layoutService;
    private final LayoutBlockEntityMapper layoutBlockEntityMapper;

    public LayoutBlockResource(LayoutService layoutService, LayoutBlockEntityMapper layoutBlockEntityMapper) {
        this.layoutService = layoutService;
        this.layoutBlockEntityMapper = layoutBlockEntityMapper;
    }

    @Path("/{docId}/{pageNum}/add-table-from-ui")
    @POST
    @RolesAllowed(Roles.LABEL_DATA)
    public List<ResponseDto.LayoutBlock> addTablesFromUI(@PathParam("docId") UUID docId,
                                                         @PathParam("pageNum") Integer pageNum,
                                                         List<DbBlock.CreateTableBlockDto> tableBlockDtos) {
        List<DbBlock.BlockDto> blockDtos = tableBlockDtos.stream()
                .map(dto -> DbBlock.BlockDto.builder()
                        .docId(docId)
                        .pageNum(pageNum)
                        .blockType(BlockTypeEnum.TABLE)
                        .xMin(dto.xMin())
                        .xMax(dto.xMax())
                        .yMin(dto.yMin())
                        .yMax(dto.yMax())
                        .score(100)
                        .build())
                .toList();
        return layoutService.createOrUpdateBlocks(blockDtos, false, false)
                .parallelStream()
                .map(lb -> layoutBlockEntityMapper.toDto(lb, Boolean.FALSE))
                .toList();
    }

    @Path("/tags")
    @PATCH
    @RolesAllowed(Roles.TAG_TABLE)
    public List<DbBlock.UpdateBlockTagDto> updateTags(List<DbBlock.UpdateBlockTagDto> updateBlockTagDtos) {
        List<DbBlock.BlockDto> blockDtos = updateBlockTagDtos.stream()
                .map(dto -> DbBlock.BlockDto.builder()
                        .blockId(dto.blockId())
                        .tag(dto.tag())
                        .build())
                .toList();
        layoutService.createOrUpdateBlocks(blockDtos, true, false);
        return updateBlockTagDtos;
    }

    @Path("/comment")
    @PATCH
    @RolesAllowed(Roles.MAP_COA)
    public List<DbBlock.UpdateBlockCommentDto> updateComment(
            List<DbBlock.UpdateBlockCommentDto> updateBlockCommentDtos) {
        List<DbBlock.BlockDto> blockDtos = updateBlockCommentDtos.stream()
                .map(dto -> DbBlock.BlockDto.builder()
                        .blockId(dto.blockId())
                        .comment(dto.comment())
                        .build())
                .toList();
        layoutService.createOrUpdateBlocks(blockDtos, true, false);
        return updateBlockCommentDtos;
    }

    @Path("/bbox")
    @PATCH
    @RolesAllowed(Roles.LABEL_DATA)
    public Bbox updateBbox(@RestQuery Integer blockId, DbBlock.UpdateBlockBboxDto updateBlockBboxDto) {
        List<DbBlock.BlockDto> blockDtos = List.of(DbBlock.BlockDto.builder()
                .blockId(blockId)
                .xMin(updateBlockBboxDto.xMin())
                .xMax(updateBlockBboxDto.xMax())
                .yMin(updateBlockBboxDto.yMin())
                .yMax(updateBlockBboxDto.yMax())
                .build());
        layoutService.createOrUpdateBlocks(blockDtos, true, false);
        return new Bbox(updateBlockBboxDto.xMin(), updateBlockBboxDto.xMax(), updateBlockBboxDto.yMin(),
                updateBlockBboxDto.yMax());
    }

    @Path("/doc/{docId}")
    @GET
    public List<ResponseDto.LayoutBlock> list(@PathParam("docId") UUID docId,
                                              @DefaultValue("TABLE")
                                              @RestQuery BlockTypeEnum blockType) {
        return layoutService.listBlocksForDocId(docId, blockType)
                .parallelStream()
                .map(lb -> layoutBlockEntityMapper.toDto(lb, Boolean.FALSE))
                .toList();
    }

    @Path("/doc/{docId}/page/{pageNum}")
    @GET
    public List<ResponseDto.LayoutBlock> list(@PathParam("docId") UUID docId, @PathParam("pageNum") Integer pageNum,
                                              @DefaultValue("TABLE")
                                              @RestQuery BlockTypeEnum blockType) {
        return layoutService.listBlocksForDocIdAndPageNum(docId, pageNum, blockType)
                .parallelStream()
                .map(lb -> layoutBlockEntityMapper.toDto(lb, Boolean.FALSE))
                .toList();
    }

    @Path("/{blockId}")
    @GET
    public ResponseDto.LayoutBlock get(@PathParam("blockId") Integer blockId) {
        return layoutBlockEntityMapper.toDto(layoutService.getBlock(blockId), Boolean.FALSE);
    }

    @Path("/doc/{docId}/tags")
    @GET
    public List<DbBlock.BlockTagOnly> getTags(@PathParam("docId") UUID docId,
                                              @DefaultValue("TABLE")
                                              @RestQuery BlockTypeEnum blockType) {
        return layoutService.getTags(docId, blockType);
    }

    @Path("/doc/{docId}/ids")
    @GET
    public List<Integer> getBlockIdsForDoc(@PathParam("docId") UUID docId) {
        return layoutService.getBlockIdsForDoc(docId);
    }
}
