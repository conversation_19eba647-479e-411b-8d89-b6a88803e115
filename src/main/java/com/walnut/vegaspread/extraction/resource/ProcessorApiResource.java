package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.DbHeader;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.model.MappedRowDto;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.service.ExtractedRowCoaDataService;
import com.walnut.vegaspread.extraction.service.LayoutService;
import com.walnut.vegaspread.extraction.service.TableService;
import com.walnut.vegaspread.extraction.service.mapper.ExtractedTableRowCoaDataJoinEntityMapper;
import com.walnut.vegaspread.extraction.service.mapper.LayoutBlockEntityMapper;
import com.walnut.vegaspread.extraction.service.mapper.TableRowEntityMapper;
import jakarta.validation.constraints.NotNull;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.openapi.annotations.OpenAPIDefinition;
import org.eclipse.microprofile.openapi.annotations.info.Info;
import org.jboss.resteasy.reactive.RestQuery;

import java.util.List;
import java.util.UUID;

@OpenAPIDefinition(info = @Info(title = "Processor API authenticated by X-API-Key", version = "1.0"))
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/wise/processor")
@ApiKeyAuthenticate
public class ProcessorApiResource {
    private static final String LAYOUT_PREFIX = "/block";
    private static final String TABLE_PREFIX = "/table/extracted";
    private static final String ROW_COA_PREFIX = "/row-coa-data-mapping";

    private final LayoutService layoutService;
    private final TableService tableService;
    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final LayoutBlockEntityMapper layoutBlockEntityMapper;
    private final ExtractedTableRowCoaDataJoinEntityMapper extractedTableRowCoaDataJoinEntityMapper;
    private final TableRowEntityMapper tableRowEntityMapper;

    public ProcessorApiResource(LayoutService layoutService, TableService tableService,
                                ExtractedRowCoaDataService extractedRowCoaDataService,
                                LayoutBlockEntityMapper layoutBlockEntityMapper,
                                ExtractedTableRowCoaDataJoinEntityMapper extractedTableRowCoaDataJoinEntityMapper,
                                TableRowEntityMapper tableRowEntityMapper) {
        this.layoutService = layoutService;
        this.tableService = tableService;
        this.extractedRowCoaDataService = extractedRowCoaDataService;
        this.layoutBlockEntityMapper = layoutBlockEntityMapper;
        this.extractedTableRowCoaDataJoinEntityMapper = extractedTableRowCoaDataJoinEntityMapper;
        this.tableRowEntityMapper = tableRowEntityMapper;
    }

    @Path(LAYOUT_PREFIX + "/{docId}")
    @POST
    public List<ResponseDto.LayoutBlock> createBlock(@PathParam("docId") UUID docId,
                                                     List<DbBlock.CreateBlockDto> createBlockDtos) {
        List<DbBlock.BlockDto> blockDtos = createBlockDtos.stream()
                .map(dto -> DbBlock.BlockDto.builder()
                        .docId(docId)
                        .pageNum(dto.pageNum())
                        .blockType(dto.blockType())
                        .xMin(dto.xMin())
                        .xMax(dto.xMax())
                        .yMin(dto.yMin())
                        .yMax(dto.yMax())
                        .score(dto.score())
                        .tag(dto.tag())
                        .comment(dto.comment())
                        .build())
                .toList();
        return layoutService.createOrUpdateBlocks(blockDtos, false, true)
                .parallelStream()
                .map(lb -> layoutBlockEntityMapper.toDto(lb, Boolean.FALSE))
                .toList();
    }

    @Path(LAYOUT_PREFIX + "/batch")
    @POST
    public List<ResponseDto.LayoutBlock> getBlocks(List<Integer> blockIds) {
        return layoutService.getBlocks(blockIds)
                .stream()
                .map(lb -> layoutBlockEntityMapper.toDto(lb, Boolean.FALSE))
                .toList();
    }

    @Path(LAYOUT_PREFIX + "/{blockId}")
    @DELETE
    public void deleteBlockByBlockId(@PathParam("blockId") Integer blockId) {
        layoutService.deleteBlockById(blockId);
    }

    @Path(LAYOUT_PREFIX + "/batch")
    @DELETE
    public long deleteBlockBatch(List<Integer> blockIds) {
        return layoutService.deleteBlocksByIds(blockIds);
    }

    @Path(LAYOUT_PREFIX + "/doc/{docId}")
    @DELETE
    public long deleteBlocksByDocId(@PathParam("docId") UUID docId) {
        return layoutService.deleteBlocksByDocId(docId);
    }

    @Path(LAYOUT_PREFIX + "/tags")
    @PATCH
    public List<DbBlock.UpdateBlockTagByProcessorDto> updateBlocks(
            List<DbBlock.UpdateBlockTagByProcessorDto> updateBlockTagDtos) {
        List<DbBlock.BlockDto> blockDtos = updateBlockTagDtos.stream()
                .map(dto -> DbBlock.BlockDto.builder()
                        .blockId(dto.blockId())
                        .tag(dto.tag())
                        .tagExplainabilityId(dto.tagExplainabilityId())
                        .build())
                .toList();
        layoutService.createOrUpdateBlocks(blockDtos, true, true);
        return updateBlockTagDtos;
    }

    @Path(LAYOUT_PREFIX + "/doc/{docId}/tags")
    @GET
    public List<DbBlock.BlockTagOnly> getTableTags(@PathParam("docId") UUID docId) {
        return layoutService.getTags(docId, BlockTypeEnum.TABLE);
    }

    @Path(TABLE_PREFIX + "/headers")
    @POST
    public List<DbHeader.InsertHeaderDtoForProcessor> createTableHeaders(
            List<DbHeader.InsertHeaderDtoForProcessor> insertHeaderDtos,
            @QueryParam("deleteExisting") boolean deleteExisting) {
        List<DbHeader.HeaderDto> headerDtos = insertHeaderDtos.stream()
                .map(insertHeaderDto -> DbHeader.HeaderDto.builder()
                        .tableId(insertHeaderDto.tableId())
                        .headerId(insertHeaderDto.headerId())
                        .text(insertHeaderDto.text())
                        .xMin(insertHeaderDto.xMin())
                        .xMax(insertHeaderDto.xMax())
                        .yMin(insertHeaderDto.yMin())
                        .yMax(insertHeaderDto.yMax())
                        .score(insertHeaderDto.score())
                        .build())
                .toList();
        tableService.createOrUpdateHeaders(headerDtos, false, deleteExisting);
        return insertHeaderDtos;
    }

    @Path(TABLE_PREFIX + "/rows")
    @POST
    public List<DbRow.InsertRowDtoForProcessor> createTableRows(List<DbRow.InsertRowDtoForProcessor> insertRowDtos,
                                                                @QueryParam("deleteExisting") boolean deleteExisting) {
        List<DbRow.RowDto> rowDtos = insertRowDtos.stream()
                .map(insertRowDto -> DbRow.RowDto.builder()
                        .tableId(insertRowDto.tableId())
                        .rowId(insertRowDto.rowId())
                        .cellsText(insertRowDto.cellsText())
                        .parentText(insertRowDto.parentText())
                        .xMin(insertRowDto.xMin())
                        .xMax(insertRowDto.xMax())
                        .yMin(insertRowDto.yMin())
                        .yMax(insertRowDto.yMax())
                        .score(insertRowDto.score())
                        .comment(insertRowDto.comment())
                        .ntaTableId(0)
                        .build())
                .toList();
        tableService.createOrUpdateRows(rowDtos, false, true, deleteExisting);
        return insertRowDtos;
    }

    @Path(TABLE_PREFIX + "/headers")
    @DELETE
    public long deleteTableHeaders(List<Integer> tableIds) {
        return tableService.deleteHeaders(tableIds);
    }

    @Path(TABLE_PREFIX + "/rows")
    @DELETE
    public long deleteTableRows(List<Integer> tableIds) {
        return tableService.deleteRows(tableIds);
    }

    @Path(TABLE_PREFIX + "/rows")
    @PATCH
    public List<ResponseDto.TableRow> updateTableRows(List<DbRow.RowDto> rowDtos) {
        List<TableRowEntity> rows = tableService.createOrUpdateRows(rowDtos, true, true, false);
        return rows.stream().map(row -> tableRowEntityMapper.toDto(row, Boolean.FALSE)).toList();
    }

    @Path(TABLE_PREFIX + "/mapped-coa")
    @POST
    public List<MappedRowDto> getMappedCoaRows(@NotNull List<UUID> docIds,
                                               @DefaultValue("false") @RestQuery @NotNull Boolean onlyActive,
                                               @DefaultValue("all") @RestQuery @NotNull String tableType) {
        return tableService.getMappedCoaRows(docIds, onlyActive, tableType);
    }

    @Path(TABLE_PREFIX + "/mapped-coa/ids")
    @POST
    public List<MappedRowDto> getMappedCoaRows(@NotNull List<Integer> explainabilityIds) {
        return tableService.getMappedCoaRowsByIds(explainabilityIds);
    }

    @Path(TABLE_PREFIX + "/mapped-coa")
    @DELETE
    public long deleteMappedCoa(@NotNull List<UUID> docIds) {
        return tableService.deleteMappedCoaRows(docIds);
    }

    @Path(ROW_COA_PREFIX)
    @POST
    public List<ExtractedTableRowCoaDataJoinDto.Response> createOrUpdateCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos) {
        List<ExtractedTableRowCoaDataJoinEntity> extractedTableRowCoaDataJoinEntities =
                extractedRowCoaDataService.createOrUpdateCoaDataMapping(extractedTableRowCoaDataJoinDtos, true);
        return extractedTableRowCoaDataJoinEntityMapper.toDtoList(extractedTableRowCoaDataJoinEntities);
    }
}
