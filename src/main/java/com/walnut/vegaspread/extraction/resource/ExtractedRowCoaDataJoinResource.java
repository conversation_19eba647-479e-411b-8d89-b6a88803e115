package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.service.ExtractedRowCoaDataService;
import com.walnut.vegaspread.extraction.service.mapper.ExtractedTableRowCoaDataJoinEntityMapper;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;

import java.util.List;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("/row-coa-data-mapping")
@Authenticated
public class ExtractedRowCoaDataJoinResource {

    private final ExtractedRowCoaDataService extractedRowCoaDataService;
    private final ExtractedTableRowCoaDataJoinEntityMapper extractedTableRowCoaDataJoinEntityMapper;

    public ExtractedRowCoaDataJoinResource(ExtractedRowCoaDataService extractedRowCoaDataService,
                                           ExtractedTableRowCoaDataJoinEntityMapper extractedTableRowCoaDataJoinEntityMapper) {
        this.extractedRowCoaDataService = extractedRowCoaDataService;
        this.extractedTableRowCoaDataJoinEntityMapper = extractedTableRowCoaDataJoinEntityMapper;
    }

    @POST
    @RolesAllowed(Roles.MAP_COA)
    public List<ExtractedTableRowCoaDataJoinDto.Response> createOrUpdateCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos) {
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> userExtractedTableRowCoaDataJoinDtos =
                extractedTableRowCoaDataJoinDtos.stream()
                        .map(extractedTableRowCoaDataJoinDto -> new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(
                                extractedTableRowCoaDataJoinDto.tableId(),
                                extractedTableRowCoaDataJoinDto.rowId(),
                                extractedTableRowCoaDataJoinDto.coaData(),
                                null)
                        ).toList();
        List<ExtractedTableRowCoaDataJoinEntity> extractedTableRowCoaDataJoinEntities =
                extractedRowCoaDataService.createOrUpdateCoaDataMapping(userExtractedTableRowCoaDataJoinDtos, false);
        return extractedTableRowCoaDataJoinEntityMapper.toDtoList(extractedTableRowCoaDataJoinEntities);
    }
}
