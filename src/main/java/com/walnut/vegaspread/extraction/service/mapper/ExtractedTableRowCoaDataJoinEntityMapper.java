package com.walnut.vegaspread.extraction.service.mapper;

import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.service.CoaDataService;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;

@ApplicationScoped
public class ExtractedTableRowCoaDataJoinEntityMapper {

    private final CoaDataService coaDataService;

    public ExtractedTableRowCoaDataJoinEntityMapper(CoaDataService coaDataService) {
        this.coaDataService = coaDataService;
    }

    public List<ExtractedTableRowCoaDataJoinDto.Response> toDtoList(
            List<ExtractedTableRowCoaDataJoinEntity> extractedTableRowCoaDataJoinEntities) {
        return extractedTableRowCoaDataJoinEntities.stream()
                .map(this::toDto)
                .toList();
    }

    public ExtractedTableRowCoaDataJoinDto.Response toDto(
            ExtractedTableRowCoaDataJoinEntity extractedTableRowCoaDataJoinEntity) {
        CoaDataEntity coaDataEntity = coaDataService.findById(
                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId());
        return new ExtractedTableRowCoaDataJoinDto.Response(
                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId(),
                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                        .getTableRowPkId()
                        .getRowId()
                        .intValue(),
                new CoaDataDto.Response(
                        coaDataEntity.getId(),
                        coaDataEntity.getCoaId(),
                        coaDataEntity.getCoaScore().intValue(),
                        coaDataEntity.getUseCoa()
                ),
                extractedTableRowCoaDataJoinEntity.explainability == null ? null : CoaMappingEntity.toDto(
                        extractedTableRowCoaDataJoinEntity.explainability)
        );
    }
}