package com.walnut.vegaspread.extraction.service.mapper;

import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.service.LayoutService;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.jboss.logging.Logger;

import java.util.Comparator;
import java.util.UUID;

@ApplicationScoped
public class LayoutBlockEntityMapper {
    private final TableRowEntityMapper tableRowEntityMapper;
    private final LayoutService layoutService;
    private final Logger logger = Logger.getLogger(LayoutBlockEntityMapper.class);

    public LayoutBlockEntityMapper(TableRowEntityMapper tableRowEntityMapper, LayoutService layoutService) {
        this.tableRowEntityMapper = tableRowEntityMapper;
        this.layoutService = layoutService;
    }

    @Transactional
    public ResponseDto.LayoutBlock toDto(LayoutBlockEntity block, Boolean isNtaBlock) {
        Integer tagExplainabilityId = block.getTagExplainabilityId();
        Integer tagExplainabilityPageNum = 0;
        UUID tagExplainabilityDocId = null;
        if (tagExplainabilityId != 0) {
            LayoutBlockEntity explainabilityBlock = layoutService.getBlock(tagExplainabilityId);
            if (explainabilityBlock == null) {
                logger.errorf("Block %d has invalid tagExplainabilityId %d", block.getBlockId(), tagExplainabilityId);
                tagExplainabilityId = 0;
            } else {
                tagExplainabilityPageNum = Integer.valueOf(explainabilityBlock.getPageNum());
                tagExplainabilityDocId = explainabilityBlock.getDocId();
            }
        }
        return new ResponseDto.LayoutBlock(block.getBlockId(), block.getDocId(),
                block.getPageNum(),
                block.getBlockType(), block.getTag(), block.getComment(), block.getBbox().getXMin(),
                block.getBbox().getXMax(), block.getBbox().getYMin(), block.getBbox().getYMax(),
                block.getScore(), tagExplainabilityId, tagExplainabilityPageNum, tagExplainabilityDocId,
                block.getTableHeaders()
                        .stream()
                        .sorted(Comparator.comparingInt(TableHeaderEntity::getPos))
                        .map(TableHeaderEntity::toDto)
                        .toList(),
                block.getTableRows()
                        .parallelStream()
                        .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                        .map(tr -> tableRowEntityMapper.toDto(tr, isNtaBlock))
                        .toList()
        );
    }
}
