package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.model.audit.extraction.SubtotalMappingAuditDto;
import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity;
import com.walnut.vegaspread.extraction.model.SubtotalMappingDto;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.SubtotalMappingRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.RollbackException;
import jakarta.transaction.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

@ApplicationScoped
public class SubtotalMappingService {
    public static final Integer REMOVE_SUBTOTAL_ID = 0;
    private final SubtotalMappingRepository subtotalMappingRepository;
    private final ExchangeService exchangeService;
    private final SubtotalService subtotalService;

    public SubtotalMappingService(SubtotalMappingRepository subtotalMappingRepository,
                                  ExchangeService exchangeService,
                                  SubtotalService subtotalService) {
        this.subtotalMappingRepository = subtotalMappingRepository;
        this.exchangeService = exchangeService;
        this.subtotalService = subtotalService;
    }

    public List<SubtotalMappingEntity> getSubtotalMappings(Integer tableId) {
        return subtotalMappingRepository.getByTableId(tableId);
    }

    public Optional<SubtotalMappingEntity> getSubtotalMappingForRow(TableRowPkId tableRowPkId) {
        return subtotalMappingRepository.findByIdOptional(tableRowPkId);
    }

    @Transactional
    public List<SubtotalMappingEntity> createOrUpdateMappings(
            List<SubtotalMappingDto.CreateOrUpdateMapping> subtotalMappingDtos, UUID docId) {

        List<SubtotalMappingAuditDto.Create> subtotalMappingCreateAuditDtos = new ArrayList<>();
        List<SubtotalMappingAuditDto.Update> subtotalMappingUpdateAuditDtos = new ArrayList<>();
        List<SubtotalMappingEntity> subtotalMappingEntities = new ArrayList<>();

        for (SubtotalMappingDto.CreateOrUpdateMapping subtotalMappingDto : subtotalMappingDtos) {
            SubtotalEntity subtotal = subtotalService.getSubtotalForId(subtotalMappingDto.subtotalId());

            Integer tableId = subtotalMappingDto.tableId();
            Integer rowId = subtotalMappingDto.rowId();

            SubtotalMappingEntity subtotalMappingEntity = subtotalMappingRepository.findById(
                    new TableRowPkId(tableId, rowId));

            if (subtotalMappingEntity != null) {
                subtotalMappingUpdateAuditDtos.add(
                        new SubtotalMappingAuditDto.Update(tableId, rowId, SubtotalMappingEntity.SUBTOTAL_ID_COL_NAME,
                                subtotalMappingEntity.getSubtotal().getId().toString(),
                                subtotalMappingDto.subtotalId().toString()));
                subtotalMappingEntity.setSubtotal(subtotal);
                subtotalMappingEntities.add(subtotalMappingEntity);
            } else if (subtotalMappingDto.subtotalId().intValue() != REMOVE_SUBTOTAL_ID) {
                subtotalMappingEntity = SubtotalMappingEntity.builder()
                        .id(new TableRowPkId(tableId, rowId))
                        .subtotal(subtotal)
                        .docId(docId)
                        .build();
                subtotalMappingCreateAuditDtos.add(
                        new SubtotalMappingAuditDto.Create(tableId, rowId, SubtotalMappingEntity.SUBTOTAL_ID_COL_NAME,
                                subtotalMappingDto.subtotalId().toString()));
                subtotalMappingEntities.add(subtotalMappingEntity);
            }
        }

        subtotalMappingRepository.persist(subtotalMappingEntities);
        exchangeService.auditSubtotalMappingCreate(subtotalMappingCreateAuditDtos);
        exchangeService.auditSubtotalMappingUpdate(subtotalMappingUpdateAuditDtos);
        return subtotalMappingEntities;
    }

    @Transactional
    public long deleteMappings(List<SubtotalMappingDto.CreateOrUpdateMapping> subtotalMappingDtos,
                               UUID docId) throws RollbackException, IllegalArgumentException {
        AtomicLong deleteCount = new AtomicLong();
        List<SubtotalMappingAuditDto.Delete> subtotalMappingDeleteAuditDtos = new ArrayList<>();
        List<Integer> duplicateSubtotalIds = subtotalMappingDtos.stream()
                .map(SubtotalMappingDto.CreateOrUpdateMapping::subtotalId)
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet().stream()
                .filter(entry -> entry.getValue() > 1)
                .map(Map.Entry::getKey)
                .toList();

        if (!duplicateSubtotalIds.isEmpty()) {
            throw new IllegalArgumentException(
                    "Invalid input data: Duplicate subtotal ids " + duplicateSubtotalIds
                            + " for document " + docId);
        }

        for (SubtotalMappingDto.CreateOrUpdateMapping subtotalMappingDto : subtotalMappingDtos) {
            Integer subtotalId = subtotalMappingDto.subtotalId();

            if (subtotalId.intValue() == REMOVE_SUBTOTAL_ID.intValue()) {
                subtotalMappingDeleteAuditDtos.add(
                        new SubtotalMappingAuditDto.Delete(subtotalMappingDto.tableId(), subtotalMappingDto.rowId(),
                                SubtotalMappingEntity.SUBTOTAL_ID_COL_NAME,
                                subtotalMappingDto.subtotalId().toString()));
                subtotalMappingRepository.deleteById(new TableRowPkId(subtotalMappingDto.tableId(),
                        subtotalMappingDto.rowId()));

                continue;
            }
            Optional<SubtotalMappingEntity> optMappingForSubtotalId =
                    subtotalMappingRepository.findByDocIdAndSubtotalId(docId,
                            subtotalMappingDto.subtotalId());

            if (optMappingForSubtotalId.isPresent()) {
                SubtotalMappingEntity mappingForSubtotalId = optMappingForSubtotalId.get();

                if (!mappingForSubtotalId.getId()
                        .equals(new TableRowPkId(subtotalMappingDto.tableId(), subtotalMappingDto.rowId()))) {
                    subtotalMappingDeleteAuditDtos.add(
                            new SubtotalMappingAuditDto.Delete(mappingForSubtotalId.getId().getTableId(),
                                    mappingForSubtotalId.getId().getRowId().intValue(),
                                    SubtotalMappingEntity.SUBTOTAL_ID_COL_NAME,
                                    mappingForSubtotalId.getSubtotal().getId().toString()));
                }
                long deleteCountForSubtotalId = subtotalMappingRepository.deleteMappingForSubtotalId(
                        mappingForSubtotalId);

                if (deleteCountForSubtotalId < 1) {
                    throw new RollbackException(
                            "Failed to delete existing mapping " + mappingForSubtotalId);
                }
                deleteCount.getAndIncrement();
            }
        }
        exchangeService.auditSubtotalMappingDelete(subtotalMappingDeleteAuditDtos);
        return deleteCount.longValue();
    }

    public List<SubtotalMappingEntity> getSubtotalMappingsForDoc(UUID docId) {
        return subtotalMappingRepository.getByDocId(docId);
    }
}
