package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.clients.AuditClient;
import com.walnut.vegaspread.common.clients.ClientFactory;
import com.walnut.vegaspread.common.clients.CoaClient;
import com.walnut.vegaspread.common.clients.WorkflowClient;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableHeaderAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.SubtotalMappingAuditDto;
import com.walnut.vegaspread.common.model.coa.CoaItem;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.model.extraction.DocData;
import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.jboss.logging.Logger;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@ApplicationScoped
public class ExchangeService {
    public static final int CHUNK_SIZE = 100;
    private static final Logger logger = Logger.getLogger(ExchangeService.class);
    private final AuditClient auditClient;
    private final CoaClient coaClient;
    private final WorkflowClient workflowClient;

    public ExchangeService(@ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName) {
        this.auditClient = ClientFactory.createClient(AuditClient.class, envName);
        this.coaClient = ClientFactory.createClient(CoaClient.class, envName);
        this.workflowClient = ClientFactory.createClient(WorkflowClient.class, envName);
    }

    private <T> List<List<T>> createChunk(List<T> list) {
        List<List<T>> chunkedList = new ArrayList<>();

        for (int i = 0; i < list.size(); i += CHUNK_SIZE) {
            int end = Math.min(list.size(), i + CHUNK_SIZE);
            chunkedList.add(new ArrayList<>(list.subList(i, end)));
        }

        return chunkedList;
    }

    public void auditUpdateBlocks(List<LayoutBlockAuditDto.Update> blocks, Boolean isApiKeyAuthenticated) {
        List<LayoutBlockAuditDto.Update> filteredBlocks = blocks.stream()
                .distinct()
                .filter(block -> !Objects.equals(block.newValue(), block.prevValue()))
                .toList();
        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            for (List<LayoutBlockAuditDto.Update> blockChunk : createChunk(filteredBlocks)) {
                auditClient.layoutBlockAuditForUpdateByProcessor(blockChunk);
            }
        } else {
            for (List<LayoutBlockAuditDto.Update> blockChunk : createChunk(filteredBlocks)) {
                auditClient.layoutBlockAuditForUpdate(blockChunk);
            }
        }
    }

    public void auditCreateBlocks(List<LayoutBlockAuditDto.Create> blocks, Boolean isApiKeyAuthenticated) {
        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            for (List<LayoutBlockAuditDto.Create> blockChunk : createChunk(blocks)) {
                auditClient.layoutBlockAuditForCreateByProcessor(blockChunk);
            }
        } else {
            for (List<LayoutBlockAuditDto.Create> blockChunk : createChunk(blocks)) {
                auditClient.layoutBlockAuditForCreate(blockChunk);
            }
        }
    }

    public void auditFirstAndLastBlocksForDelete(List<Integer> layoutBlockIds) {
        if (layoutBlockIds == null || layoutBlockIds.isEmpty()) {
            logger.error("Empty block list");
        } else if (layoutBlockIds.size() == 1) {
            auditClient.layoutBlockAuditForDeleteByProcessor(
                    List.of(new LayoutBlockAuditDto.Delete(layoutBlockIds.get(0),
                            LayoutBlockEntity.BLOCK_ID_COL_NAME, layoutBlockIds.get(0).toString())));
        } else {
            int minBlockId = Collections.min(layoutBlockIds);
            int maxBlockId = Collections.max(layoutBlockIds);
            auditClient.layoutBlockAuditForDeleteByProcessor(List.of(
                    new LayoutBlockAuditDto.Delete(minBlockId, LayoutBlockEntity.BLOCK_ID_COL_NAME,
                            String.valueOf(minBlockId)),
                    new LayoutBlockAuditDto.Delete(maxBlockId, LayoutBlockEntity.BLOCK_ID_COL_NAME,
                            String.valueOf(maxBlockId))
            ));
        }
    }

    public void auditRows(List<ExtractedTableRowAuditDto.Update> rows, Boolean isApiKeyAuthenticated) {
        if (rows == null || rows.isEmpty()) {
            return;
        }
        List<ExtractedTableRowAuditDto.Update> filteredRows = rows.stream()
                .distinct()
                .filter(block -> !Objects.equals(block.newValue(), block.prevValue()))
                .toList();

        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            for (List<ExtractedTableRowAuditDto.Update> rowChunk : createChunk(filteredRows)) {
                auditClient.extractedTableRowAuditForUpdateByProcessor(rowChunk);
            }
        } else {
            for (List<ExtractedTableRowAuditDto.Update> rowChunk : createChunk(filteredRows)) {
                auditClient.extractedTableRowAuditForUpdate(rowChunk);
            }
        }
    }

    public void auditCreateRows(List<ExtractedTableRowAuditDto.Create> rows) {
        for (List<ExtractedTableRowAuditDto.Create> rowChunk : createChunk(rows)) {
            auditClient.extractedTableRowAuditForCreate(rowChunk);
        }
    }

    public void auditExtractedRowCoaDataCreate(List<ExtractedRowCoaDataAuditDto.Create> createMappingDtos,
                                               Boolean isApiKeyAuthenticated) {
        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            for (List<ExtractedRowCoaDataAuditDto.Create> mappingChunk : createChunk(createMappingDtos)) {
                auditClient.extractedRowCoaDataAuditForCreateByProcessor(mappingChunk);
            }
        } else {
            for (List<ExtractedRowCoaDataAuditDto.Create> mappingChunk : createChunk(createMappingDtos)) {
                auditClient.extractedRowCoaDataAuditForCreate(mappingChunk);
            }
        }
    }

    public void auditExtractedRowCoaDataUpdate(List<ExtractedRowCoaDataAuditDto.Update> updateMappingDtos,
                                               Boolean isApiKeyAuthenticated) {
        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            for (List<ExtractedRowCoaDataAuditDto.Update> mappingChunk : createChunk(updateMappingDtos)) {
                auditClient.extractedRowCoaDataAuditForUpdateByProcessor(mappingChunk);
            }
        } else {
            for (List<ExtractedRowCoaDataAuditDto.Update> mappingChunk : createChunk(updateMappingDtos)) {
                auditClient.extractedRowCoaDataAuditForUpdate(mappingChunk);
            }
        }
    }

    public void auditExtractedRowCoaDataDelete(List<ExtractedRowCoaDataAuditDto.Delete> deleteMappingDtos,
                                               Boolean isApiKeyAuthenticated) {
        if (Boolean.TRUE.equals(isApiKeyAuthenticated)) {
            for (List<ExtractedRowCoaDataAuditDto.Delete> mappingChunk : createChunk(deleteMappingDtos)) {
                auditClient.extractedRowCoaDataAuditForDeleteByProcessor(mappingChunk);
            }
        } else {
            for (List<ExtractedRowCoaDataAuditDto.Delete> mappingChunk : createChunk(deleteMappingDtos)) {
                auditClient.extractedRowCoaDataAuditForDelete(mappingChunk);
            }
        }
    }

    public void auditSubtotalCreate(List<SubtotalAuditDto.Create> createSubtotalDtos) {
        for (List<SubtotalAuditDto.Create> mappingChunk : createChunk(createSubtotalDtos)) {
            auditClient.subtotalAuditForCreate(mappingChunk);
        }
    }

    public void auditSubtotalUpdate(List<SubtotalAuditDto.Update> updateSubtotalDtos) {
        for (List<SubtotalAuditDto.Update> mappingChunk : createChunk(updateSubtotalDtos)) {
            auditClient.subtotalAuditForUpdate(mappingChunk);
        }
    }

    public void auditSubtotalDelete(List<SubtotalAuditDto.Delete> deleteSubtotalDtos) {
        for (List<SubtotalAuditDto.Delete> mappingChunk : createChunk(deleteSubtotalDtos)) {
            auditClient.subtotalAuditForDelete(mappingChunk);
        }
    }

    public void updateTimeInDb(UUID docId, boolean isApiKeyAuthenticated) {
        if (!isApiKeyAuthenticated) {
            workflowClient.updateTime(docId);
        }
    }

    public DocData getDocument(UUID docId) {
        return workflowClient.getDocument(docId);
    }

    public UrlDto getExcelTemplate(String clientName, String coaClientName) {
        return workflowClient.getExcelTemplate(clientName, coaClientName);
    }

    public List<CoaItem> getCoaList(String clientName) {
        return coaClient.getCoaList(clientName);
    }

    public List<CoaItemDto> getCoasFromIds(List<Integer> coaIds) {
        return coaClient.get(coaIds);
    }

    public void auditSubtotalMappingCreate(List<SubtotalMappingAuditDto.Create> createSubtotalMappingDtos) {
        for (List<SubtotalMappingAuditDto.Create> mappingChunk : createChunk(createSubtotalMappingDtos)) {
            auditClient.subtotalMappingAuditForCreate(mappingChunk);
        }
    }

    public void auditSubtotalMappingUpdate(List<SubtotalMappingAuditDto.Update> updateSubtotalMappingDtos) {
        for (List<SubtotalMappingAuditDto.Update> mappingChunk : createChunk(updateSubtotalMappingDtos)) {
            auditClient.subtotalMappingAuditForUpdate(mappingChunk);
        }
    }

    public void auditSubtotalMappingDelete(List<SubtotalMappingAuditDto.Delete> deleteSubtotalMappingDtos) {
        for (List<SubtotalMappingAuditDto.Delete> mappingChunk : createChunk(deleteSubtotalMappingDtos)) {
            auditClient.subtotalMappingAuditForDelete(mappingChunk);
        }
    }

    public void auditDeleteRows(List<ExtractedTableRowAuditDto.Delete> rows) {
        for (List<ExtractedTableRowAuditDto.Delete> mappingChunk : createChunk(rows)) {
            auditClient.extractedTableRowAuditForDelete(mappingChunk);
        }
    }

    public void auditCreateHeaders(List<ExtractedTableHeaderAuditDto.Create> headers) {
        for (List<ExtractedTableHeaderAuditDto.Create> mappingChunk : createChunk(headers)) {
            auditClient.extractedTableHeaderAuditForCreate(mappingChunk);
        }
    }

    public void auditDeleteHeaders(List<ExtractedTableHeaderAuditDto.Delete> headers) {
        for (List<ExtractedTableHeaderAuditDto.Delete> mappingChunk : createChunk(headers)) {
            auditClient.extractedTableHeaderAuditForDelete(mappingChunk);
        }
    }

    public void auditUpdateHeaders(List<ExtractedTableHeaderAuditDto.Update> headers) {
        for (List<ExtractedTableHeaderAuditDto.Update> mappingChunk : createChunk(headers)) {
            auditClient.extractedTableHeaderAuditForUpdate(mappingChunk);
        }
    }
}
