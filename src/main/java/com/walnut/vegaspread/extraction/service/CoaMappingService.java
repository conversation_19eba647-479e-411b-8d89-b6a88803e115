package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.Optional;

@ApplicationScoped
public class CoaMappingService {
    private final CoaMappingRepository coaMappingRepository;

    public CoaMappingService(CoaMappingRepository coaMappingRepository) {
        this.coaMappingRepository = coaMappingRepository;
    }

    public CoaMappingEntity findById(Integer coaMappingId) {
        return coaMappingRepository.findById(coaMappingId);
    }

    public Optional<CoaMappingEntity> findByIdOptional(Integer coaMappingId) {
        return coaMappingRepository.findByIdOptional(coaMappingId);
    }
}
