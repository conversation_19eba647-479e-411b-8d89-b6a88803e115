package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.model.audit.extraction.SubtotalAuditDto;
import com.walnut.vegaspread.common.utils.Client;
import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.model.SubtotalDto;
import com.walnut.vegaspread.extraction.repository.SubtotalRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
public class SubtotalService {
    private final SubtotalRepository subtotalRepository;
    private final ExchangeService exchangeService;

    public SubtotalService(SubtotalRepository subtotalRepository,
                           ExchangeService exchangeService) {
        this.subtotalRepository = subtotalRepository;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<SubtotalEntity> createOrUpdateOrDeleteSubtotals(
            List<SubtotalDto.CreateOrUpdate> subtotalDtos, String coaClientStr, String clientStr) {
        if (!Client.isValidClient(coaClientStr) || !Client.isValidClient(clientStr)) {
            throw new InvalidParameterException(
                    "Invalid client/s " + coaClientStr + " " + clientStr + " " +
                            "for subtotal"
            );
        }

        String coaClient = Client.valueOf(coaClientStr.toUpperCase()).getClientName();
        String client = Client.valueOf(clientStr.toUpperCase()).getClientName();

        List<SubtotalEntity> subtotalEntities = new ArrayList<>();
        List<SubtotalDto.CreateOrUpdate> subtotalCreateDtos = new ArrayList<>();
        Map<SubtotalEntity, SubtotalDto.CreateOrUpdate> subtotalUpdateMap = new HashMap<>();
        List<SubtotalAuditDto.Delete> subtotalDeleteAudits = new ArrayList<>();

        for (SubtotalDto.CreateOrUpdate subtotalDto : subtotalDtos) {

            SubtotalEntity subtotalEntity;

            Optional<SubtotalEntity> optSubtotalEntity = subtotalForExcelRow(coaClient, client,
                    subtotalDto.excelRowNumber().shortValue());

            if (optSubtotalEntity.isPresent()) {
                subtotalEntity = optSubtotalEntity.get();
                SubtotalEntity currentSubtotal = new SubtotalEntity(subtotalEntity);

                //Remove subtotals if subtotal name is null.
                if (subtotalDto.subtotalName() == null) {
                    long deleteCount = deleteSubtotal(coaClient, client,
                            subtotalDto.excelRowNumber().shortValue());
                    if (deleteCount == 1) {
                        subtotalDeleteAudits.add(
                                new SubtotalAuditDto.Delete(currentSubtotal.getId(),
                                        SubtotalEntity.SUBTOTAL_NAME_COL_NAME,
                                        currentSubtotal.getSubtotalName()));
                    }
                    continue;
                }
                subtotalUpdateMap.put(subtotalEntity, subtotalDto);
            } else if (subtotalDto.subtotalName() != null) {
                subtotalCreateDtos.add(subtotalDto);
            }
        }

        if (!subtotalUpdateMap.isEmpty()) {
            subtotalEntities.addAll(updateSubtotals(subtotalUpdateMap));
        }
        if (!subtotalCreateDtos.isEmpty()) {
            subtotalEntities.addAll(createSubtotals(coaClient, client, subtotalCreateDtos));
        }
        if (!subtotalDeleteAudits.isEmpty()) {
            exchangeService.auditSubtotalDelete(subtotalDeleteAudits);
        }

        return subtotalEntities;
    }

    @Transactional
    public List<SubtotalEntity> createSubtotals(
            String coaClient, String client, List<SubtotalDto.CreateOrUpdate> subtotalCreateDtos) {
        List<SubtotalAuditDto.Create> subtotalCreateAudits;
        List<SubtotalEntity> subtotalEntities = new ArrayList<>();

        for (SubtotalDto.CreateOrUpdate subtotalCreateDto : subtotalCreateDtos) {
            SubtotalEntity subtotalEntity = new SubtotalEntity();
            subtotalEntity.setCoaClient(coaClient);
            subtotalEntity.setClient(client);
            subtotalEntity.setExcelRowNumber(subtotalCreateDto.excelRowNumber().shortValue());
            subtotalEntity.setSubtotalName(subtotalCreateDto.subtotalName());
            subtotalEntities.add(subtotalEntity);
        }
        subtotalRepository.persist(subtotalEntities);
        subtotalCreateAudits = subtotalEntities.stream()
                .map(subtotalEntity -> new SubtotalAuditDto.Create(subtotalEntity.getId(),
                        SubtotalEntity.SUBTOTAL_NAME_COL_NAME, subtotalEntity.getSubtotalName()))
                .toList();
        exchangeService.auditSubtotalCreate(subtotalCreateAudits);
        return subtotalEntities;
    }

    @Transactional
    public List<SubtotalEntity> updateSubtotals(
            Map<SubtotalEntity, SubtotalDto.CreateOrUpdate> subtotalUpdateEntities) {

        List<SubtotalAuditDto.Update> subtotalUpdateAudits = new ArrayList<>();
        List<SubtotalEntity> subtotalEntities = new ArrayList<>();

        subtotalUpdateEntities.forEach((currentSubtotal, subtotalDto) -> {
            subtotalUpdateAudits.add(new SubtotalAuditDto.Update(currentSubtotal.getId(),
                    SubtotalEntity.SUBTOTAL_NAME_COL_NAME, currentSubtotal.getSubtotalName(),
                    subtotalDto.subtotalName()));
            currentSubtotal.setSubtotalName(subtotalDto.subtotalName());
            subtotalEntities.add(currentSubtotal);
        });

        subtotalRepository.persist(subtotalEntities);
        exchangeService.auditSubtotalUpdate(subtotalUpdateAudits);
        return subtotalEntities;
    }

    @Transactional
    public long deleteSubtotal(String coaClient, String client, Short excelRowNumber) {
        return subtotalRepository.deleteByCoaClientAndClientAndExcelRowNumber(coaClient, client, excelRowNumber);
    }

    private Optional<SubtotalEntity> subtotalForExcelRow(String coaClient, String client,
                                                         Short excelRowNumber) {
        return subtotalRepository.findByCoaClientAndClientAndExcelRowNumber(coaClient, client, excelRowNumber);
    }

    public List<SubtotalEntity> getSubtotals(String coaClient, String client) {
        return subtotalRepository.findByCoaClientAndClient(coaClient, client);
    }

    public SubtotalEntity getSubtotalForId(Integer subtotalId) {
        return subtotalRepository.findById(subtotalId);
    }
}
