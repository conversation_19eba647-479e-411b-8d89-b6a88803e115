package com.walnut.vegaspread.extraction.service;

import com.walnut.vegaspread.common.exceptions.DeletionFailedException;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedRowCoaDataAuditDto;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.primarykey.ExtractedTableRowCoaDataPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.jboss.logging.Logger;

import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@ApplicationScoped
public class ExtractedRowCoaDataService {
    private static final Logger logger = Logger.getLogger(ExtractedRowCoaDataService.class);
    private final ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    private final CoaDataService coaDataService;
    private final CoaMappingService coaMappingService;
    private final ExchangeService exchangeService;

    public ExtractedRowCoaDataService(ExtractedRowCoaDataRepository extractedRowCoaDataRepository,
                                      CoaDataService coaDataService, CoaMappingService coaMappingService,
                                      ExchangeService exchangeService) {
        this.extractedRowCoaDataRepository = extractedRowCoaDataRepository;
        this.coaDataService = coaDataService;
        this.coaMappingService = coaMappingService;
        this.exchangeService = exchangeService;
    }

    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> createOrUpdateCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaJoinDtos,
            Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction for COA mapping creation/updation");
        if (rowCoaJoinDtos == null || rowCoaJoinDtos.isEmpty()) {
            logger.debugf("No row coa mapping found");
            return Collections.emptyList();
        }
        List<ExtractedTableRowCoaDataJoinEntity> rowCoaJoinEntities = new ArrayList<>();

        Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> rowCoaUpdateDtos =
                new HashMap<>();
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaCreateDtos = new ArrayList<>();
        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaDeleteDtos = new ArrayList<>();

        rowCoaJoinDtos.forEach(rowCoaJoinDto -> {
            logger.debugf("Checking if row coa mapping exists for table and row id %s, %s", rowCoaJoinDto.tableId(),
                    rowCoaJoinDto.rowId());
            //Check for existing coa data.
            Optional<ExtractedTableRowCoaDataJoinEntity> optExistingRowCoaJoinEntity =
                    extractedRowCoaDataRepository.findByRowOptional(
                            rowCoaJoinDto.tableId(),
                            rowCoaJoinDto.rowId().shortValue());

            //Check if dto is coa data and coa id is not 1.
            if (rowCoaJoinDto.coaData() != null && rowCoaJoinDto.coaData().coaId() > 1) {
                logger.debugf("Coa data non null and coa id not 1 for table and row id %s, %s in dto.",
                        rowCoaJoinDto.tableId(), rowCoaJoinDto.rowId());
                //If coa data exists for row, update.
                if (optExistingRowCoaJoinEntity.isPresent()) {
                    logger.debugf("Coa data exists for table and row id %s, %s.", rowCoaJoinDto.tableId(),
                            rowCoaJoinDto.rowId());
                    //Update coa data
                    rowCoaUpdateDtos.put(rowCoaJoinDto, optExistingRowCoaJoinEntity.get());
                }
                // If coa data does not exist, create.
                else {
                    logger.debugf("Coa data does not exist for table and row id %s, %s.", rowCoaJoinDto.tableId(),
                            rowCoaJoinDto.rowId());
                    rowCoaCreateDtos.add(rowCoaJoinDto);
                }
            }
            //If dto has coa data and coa id is 1, delete.
            else if (rowCoaJoinDto.coaData() != null && rowCoaJoinDto.coaData().coaId() == 1) {
                logger.debugf("Coa data non null and coa id 1 for table and row id %s, %s in dto.",
                        rowCoaJoinDto.tableId(), rowCoaJoinDto.rowId());
                rowCoaDeleteDtos.add(rowCoaJoinDto);
            }
        });
        logger.debugf("Deleting row coa mapping");
        deleteRowCoaDataMappings(rowCoaDeleteDtos, isApiKeyAuthenticated, false);
        logger.debugf("Creating row coa mapping");
        rowCoaJoinEntities.addAll(createCoaDataMapping(rowCoaCreateDtos, isApiKeyAuthenticated));
        logger.debugf("Updating row coa mapping");
        rowCoaJoinEntities.addAll(updateCoaDataMapping(rowCoaUpdateDtos, isApiKeyAuthenticated));
        logger.debugf("Completed transaction for COA mapping creation/updation");
        return rowCoaJoinEntities;
    }

    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> createCoaDataMapping(
            List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaCreateDtos,
            Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction for COA mapping creation");
        List<ExtractedTableRowCoaDataJoinEntity> rowCoaCreateEntities = new ArrayList<>();
        List<ExtractedRowCoaDataAuditDto.Create> rowCoaCreateAuditDtos = new ArrayList<>();

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate rowCoaCreateDto : rowCoaCreateDtos) {
            logger.debugf("Creating row coa mapping for table and row id %s, %s", rowCoaCreateDto.tableId(),
                    rowCoaCreateDto.rowId());
            CoaMappingEntity coaMappingEntity;

            //Evaluate coa mapping for the row.

            //Set coa mapping to null for user created coa for row.
            if (Boolean.FALSE.equals(isApiKeyAuthenticated)) {
                logger.debugf("User created coa for table and row id %s, %s", rowCoaCreateDto.tableId(),
                        rowCoaCreateDto.rowId());
                coaMappingEntity = null;
            }
            //Processor created coa for row.
            else {

                Optional<CoaMappingEntity> optCoaMappingEntity;
                Integer explainabilityId = rowCoaCreateDto.explainabilityId();

                logger.debugf("Processor created coa for table and row id %s, %s", rowCoaCreateDto.tableId(),
                        rowCoaCreateDto.rowId());
                //If processor sends a non-null explainability id, get coa mapping for id.
                if (explainabilityId != null) {
                    logger.debugf("Check explainability id %s, for table and row id %s, %s ", explainabilityId,
                            rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId());
                    optCoaMappingEntity = coaMappingService.findByIdOptional(explainabilityId);

                    if (optCoaMappingEntity.isEmpty()) {
                        throw new InvalidParameterException(
                                "Invalid coa mapping id " + rowCoaCreateDto.explainabilityId());
                    }
                    coaMappingEntity = optCoaMappingEntity.get();
                    logger.debugf("Found explainability id %s, for table and row id %s, %s ", explainabilityId,
                            rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId());
                 /* We are creating a new join mapping, so audit only non-null explainability (to avoid null -> null
                     audit).*/
                    rowCoaCreateAuditDtos.add(
                            new ExtractedRowCoaDataAuditDto.Create(rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId(),
                                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                    coaMappingEntity.getId().toString()));
                }
                //If processor sends a null explainability id, set coa mapping to null.
                else {
                    logger.debugf("Explainability id is null for table and row id %s, %s", rowCoaCreateDto.tableId(),
                            rowCoaCreateDto.rowId());
                    coaMappingEntity = null;
                }
            }

            //Evaluate coa data for the row.

            CoaDataDto.Create coaData = rowCoaCreateDto.coaData();
            Integer tableId = rowCoaCreateDto.tableId();
            Integer rowId = rowCoaCreateDto.rowId();

           /* Find the coa data entity for the given useCoa,coaId and coaScore.If missing, add coa data and get the
             entity.*/
            CoaDataEntity joinCoaData = coaDataService.findByUseCoaAndCoaIdAndCoaScore(
                            coaData.useCoa(), coaData.coaId(), coaData.coaScore())
                    .orElseGet(() -> {
                        logger.debugf("Adding new coa data for useCoa %s, coaId %s and coaScore %s", coaData.useCoa(),
                                coaData.coaId(), coaData.coaScore());
                        return coaDataService.addCoaData(coaData);
                    });
            logger.debugf("Adding coa data id mapping %s for table id %s and row id %s ", joinCoaData.getId(),
                    rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId());
            // Add the join mapping using the evaluated coa mapping and coa data.
            rowCoaCreateEntities.add(new ExtractedTableRowCoaDataJoinEntity(new TableRowPkId(tableId, rowId),
                    joinCoaData.getId(), coaMappingEntity));

            rowCoaCreateAuditDtos.add(new ExtractedRowCoaDataAuditDto.Create(
                            rowCoaCreateDto.tableId(), rowCoaCreateDto.rowId(), TableRowEntity.COA_ID_COL_NAME,
                            rowCoaCreateDto.coaData().coaId().toString()
                    )
            );
        }
        logger.debugf("Saving rows and coa data ids for create");
        extractedRowCoaDataRepository.saveRowCoaJoin(rowCoaCreateEntities);
        logger.debugf("Saving audits for create");
        exchangeService.auditExtractedRowCoaDataCreate(rowCoaCreateAuditDtos, isApiKeyAuthenticated);
        logger.debugf("Rows and coa data mappings created");
        logger.debugf("Create transaction completed successfully");
        return rowCoaCreateEntities;
    }

    @Transactional
    public List<ExtractedTableRowCoaDataJoinEntity> updateCoaDataMapping(
            Map<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate, ExtractedTableRowCoaDataJoinEntity> rowCoaJoinUpdateDtos,
            Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction for COA mapping updation");
        List<ExtractedTableRowCoaDataJoinEntity> rowCoaUpdateEntities = new ArrayList<>();
        List<ExtractedRowCoaDataAuditDto.Update> rowCoaUpdateAuditDtos = new ArrayList<>();

        rowCoaJoinUpdateDtos.forEach((rowCoaJoinUpdateDto, rowCoaJoinUpdateEntity) -> {
            Integer tableId = rowCoaJoinUpdateDto.tableId();
            Integer rowId = rowCoaJoinUpdateDto.rowId();
            ExtractedTableRowCoaDataPkId coaDataPkId = rowCoaJoinUpdateEntity.getExtractedTableRowCoaDataPkId();

            //Get the existing join mapping.
            ExtractedTableRowCoaDataJoinEntity currentMapping = new ExtractedTableRowCoaDataJoinEntity(
                    coaDataPkId.getTableRowPkId(), coaDataPkId.getCoaDataId(),
                    rowCoaJoinUpdateEntity.getExplainability());
            logger.debugf("Existing mapping for table id %s and row id %s is coa data id %s and explainability %s",
                    tableId, rowId, coaDataPkId.getCoaDataId(),
                    rowCoaJoinUpdateEntity.getExplainability() == null ? null :
                            rowCoaJoinUpdateEntity.getExplainability()
                                    .getId());
            CoaDataDto.Create coaData = rowCoaJoinUpdateDto.coaData();

            //Check if coa data has been updated.(If one or more of useCoa,coaId or coaScore has changed).
            if (coaDataService.isUpdatedCoaData(currentMapping, coaData)) {
                logger.debugf("UseCoa, coaId or coaScore changed for coa data id %s and table id %s row id %s",
                        coaDataPkId.getCoaDataId(), tableId, rowId);
                Integer currentCoaId = getCoaIdFromCoaDataId(coaDataPkId.getCoaDataId());
                logger.debugf("Current coa id for table is %s and row id %s is %s", tableId, rowId, currentCoaId);
                logger.debugf("Deleting existing mapping for table is %s and row id %s and coa id %s for update",
                        tableId, rowId, currentCoaId);
                //Delete existing mapping to avoid concurrent update error.
                deleteRowCoaDataMapping(tableId, rowId, isApiKeyAuthenticated, true);

                //Get the coa data entity for the new information, or add if missing and get the new entity.
                CoaDataEntity coaDataMapping = coaDataService.findByUseCoaAndCoaIdAndCoaScore(coaData.useCoa(),
                                coaData.coaId(), coaData.coaScore())
                        .orElseGet(() -> {
                            logger.debugf("Adding new coa data for useCoa %s, coaId %s and coaScore %s",
                                    coaData.useCoa(), coaData.coaId(), coaData.coaScore());
                            return coaDataService.addCoaData(coaData);
                        });

                Integer coaDataId = coaDataMapping.getId();
                logger.debugf("Updating existing coa data id mapping with %s for table %s and row %s", coaDataId,
                        tableId, rowId);
                //Set the new coa data.
                currentMapping.getExtractedTableRowCoaDataPkId().setCoaDataId(coaDataId);
                Integer updatedCoaId = getCoaIdFromCoaDataId(coaDataId);
                logger.debugf("Checking if coa id has changed for table id %s and row id %s", tableId, rowId);
                // If coa id has been updated audit it.
                if (!Objects.equals(updatedCoaId, currentCoaId)) {
                    logger.debugf("Auditing updated coa id from %s to %s for table id %s and row id %s", currentCoaId,
                            updatedCoaId, tableId, rowId);
                    rowCoaUpdateAuditDtos.add(
                            new ExtractedRowCoaDataAuditDto.Update(tableId, rowId, CoaDataEntity.COA_ID_COL_NAME,
                                    currentCoaId.toString(), updatedCoaId.toString()
                            )
                    );
                    //If coa id has been updated, update explainability in the mapping if needed.
                    updateExplainability(isApiKeyAuthenticated, rowCoaJoinUpdateDto, currentMapping,
                            rowCoaUpdateAuditDtos);
                }
            }
            rowCoaUpdateEntities.add(currentMapping);
        });
        logger.debugf("Saving updated row and coa data id mappings");
        extractedRowCoaDataRepository.saveRowCoaJoin(rowCoaUpdateEntities);
        logger.debugf("Creating audits for updates");
        exchangeService.auditExtractedRowCoaDataUpdate(rowCoaUpdateAuditDtos, isApiKeyAuthenticated);
        logger.debugf("Update audits created.");
        logger.debugf("Transaction completed for coa mapping updation");

        return rowCoaUpdateEntities;
    }

    private Integer getCoaIdFromCoaDataId(Integer coaDataId) {
        return coaDataService.findById(coaDataId).getCoaId();
    }

    public ExtractedTableRowCoaDataJoinEntity updateExplainability(Boolean isApiKeyAuthenticated,
                                                                   ExtractedTableRowCoaDataJoinDto.CreateOrUpdate rowCoaJoinUpdateDto,
                                                                   ExtractedTableRowCoaDataJoinEntity rowCoaJoinEntity,
                                                                   List<ExtractedRowCoaDataAuditDto.Update> rowCoaUpdateAuditDtos) {
        //Check if explainability has been updated.
        if (!Objects.equals(
                Optional.ofNullable(rowCoaJoinEntity.explainability).map(CoaMappingEntity::getId).orElse(null),
                rowCoaJoinUpdateDto.explainabilityId())
        ) {
            //If it is a user update, or if it is a processor update and new value is null. Audit and set value to null.
            if (Boolean.FALSE.equals(isApiKeyAuthenticated) || (Boolean.TRUE.equals(
                    isApiKeyAuthenticated) && rowCoaJoinUpdateDto.explainabilityId() == null)) {
                rowCoaUpdateAuditDtos.add(
                        new ExtractedRowCoaDataAuditDto.Update(
                                rowCoaJoinEntity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId(),
                                rowCoaJoinEntity.getExtractedTableRowCoaDataPkId()
                                        .getTableRowPkId()
                                        .getRowId()
                                        .intValue(),
                                ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                rowCoaJoinEntity.getExplainability().getId().toString(), null
                        )
                );
                rowCoaJoinEntity.setExplainability(null);
            }
            // If processor updated and explainability is non-null.
            else {
                //Find the coa mapping for the explainability id.
                Optional<CoaMappingEntity> optCoaMappingEntity = coaMappingService.findByIdOptional(
                        rowCoaJoinUpdateDto.explainabilityId());
                if (optCoaMappingEntity.isEmpty()) {
                    throw new InvalidParameterException(
                            "Invalid coa mapping id " + rowCoaJoinUpdateDto.explainabilityId());
                }
                //Audit and set the new coa mapping.
                else {
                    rowCoaUpdateAuditDtos.add(
                            new ExtractedRowCoaDataAuditDto.Update(
                                    rowCoaJoinEntity.getExtractedTableRowCoaDataPkId().getTableRowPkId().getTableId(),
                                    rowCoaJoinEntity.getExtractedTableRowCoaDataPkId()
                                            .getTableRowPkId()
                                            .getRowId()
                                            .intValue(),
                                    ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                    rowCoaJoinEntity.getExplainability() == null ? null :
                                            rowCoaJoinEntity.getExplainability()
                                                    .getId()
                                                    .toString(),
                                    optCoaMappingEntity.get().getId().toString()
                            )
                    );
                    rowCoaJoinEntity.setExplainability(optCoaMappingEntity.get());
                }
            }
        }
        return rowCoaJoinEntity;
    }

    public void deleteRowCoaDataMappings(List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> rowCoaJoinDeleteDtos,
                                         Boolean isApiKeyAuthenticated, boolean isUpdate) {

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate rowCoaJoinDeleteDto : rowCoaJoinDeleteDtos) {
            logger.debugf("Deleting row and coa data id mapping for table id %s and row id %s",
                    rowCoaJoinDeleteDto.tableId(), rowCoaJoinDeleteDto.rowId());
            deleteRowCoaDataMapping(rowCoaJoinDeleteDto.tableId(), rowCoaJoinDeleteDto.rowId(), isApiKeyAuthenticated,
                    isUpdate);
        }
    }

    @Transactional
    public void deleteRowCoaDataMapping(Integer tableId, Integer rowId, Boolean isApiKeyAuthenticated,
                                        boolean isUpdate) {
        List<ExtractedRowCoaDataAuditDto.Delete> rowCoaDeleteAuditDtos = new ArrayList<>();
        logger.debugf("Starting transaction for row coa mapping deletion");
        //Find the join mapping for deletion.
        Optional<ExtractedTableRowCoaDataJoinEntity> optRowCoaJoinEntity =
                extractedRowCoaDataRepository.findByRowOptional(
                        tableId, rowId.shortValue());
        if (optRowCoaJoinEntity.isEmpty()) {
            throw new InvalidParameterException(
                    "Row coa data mapping cannot be deleted due to missing row for table " + tableId + " row " + rowId);
        }
        // Audit deletion, if the join mapping is not being deleted as a part of update(since that is an update audit).
        else if (!isUpdate) {
            logger.debugf(
                    "Adding row coa mapping deletion for audit since it is not part of update for table is %s and row" +
                            " is %s",
                    tableId, rowId);
            //Find the join entity.
            ExtractedTableRowCoaDataJoinEntity coaDataJoinEntity = optRowCoaJoinEntity.get();
            //Audit coa id deletion.
            rowCoaDeleteAuditDtos.add(
                    new ExtractedRowCoaDataAuditDto.Delete(tableId, rowId, CoaDataEntity.COA_ID_COL_NAME,
                            getCoaIdFromCoaDataId(
                                    coaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId()).toString()));
            // If existing explainability is non-null(To avoid null->null audit), audit deletion.
            if (coaDataJoinEntity.getExplainability() != null) {
                rowCoaDeleteAuditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(tableId, rowId,
                        ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                        coaDataJoinEntity.getExplainability()
                                .getId()
                                .toString()));
            }
        }
        logger.debugf("Flushing row coa mapping deletion for table id %s and row id %s", tableId, rowId);

        //Delete the join mapping.
        long deleteCount = extractedRowCoaDataRepository.deleteMappedCoaData(tableId, rowId.shortValue());

        if (deleteCount != 1) {
            throw new DeletionFailedException(
                    "Failed to delete coa data mapping for row " + rowId + " in table " + tableId);
        }
        logger.debugf("Auditing deletion for table id %s and row id %s", tableId, rowId);
        exchangeService.auditExtractedRowCoaDataDelete(rowCoaDeleteAuditDtos, isApiKeyAuthenticated);
        logger.debugf("Audit created for row coa mapping deletion for table id %s and row id %s", tableId, rowId);
        logger.debugf("Transaction completed for deletion");
    }

    @Transactional
    public void deleteTableCoaDataMappings(List<Integer> tableIds, Boolean isApiKeyAuthenticated) {
        logger.debugf("Starting transaction to delete row coa mappings for multiple tables");
        // Find join mappings for the tables.
        List<ExtractedTableRowCoaDataJoinEntity> coaDataJoinEntitiesForTables =
                extractedRowCoaDataRepository.findByTableIds(tableIds);
        //Create audit entities for coa id deletion and explainability if non-nul(To avoid null->null audit).
        List<ExtractedRowCoaDataAuditDto.Delete> extractedRowCoaDataAuditDeleteDtos =
                coaDataJoinEntitiesForTables.stream()
                        .flatMap(extractedTableRowCoaDataJoinEntity -> {
                                    List<ExtractedRowCoaDataAuditDto.Delete> deleteAuditDtos = new ArrayList<>();
                                    ExtractedRowCoaDataAuditDto.Delete rowCoaDeleteMappingAudit =
                                            new ExtractedRowCoaDataAuditDto.Delete(
                                                    extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                            .getTableRowPkId().getTableId(),
                                                    extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                            .getTableRowPkId().getRowId().intValue(),
                                                    CoaDataEntity.COA_ID_COL_NAME,
                                                    getCoaIdFromCoaDataId(
                                                            extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                                    .getCoaDataId()).toString()
                                            );
                                    deleteAuditDtos.add(rowCoaDeleteMappingAudit);
                                    logger.debugf("Creating delete mapping entry for table id %s row id %s and coa id" +
                                                    " %s",
                                            rowCoaDeleteMappingAudit.tableId(), rowCoaDeleteMappingAudit.tableId(),
                                            rowCoaDeleteMappingAudit.prevValue());
                                    if (extractedTableRowCoaDataJoinEntity.getExplainability() != null) {
                                        deleteAuditDtos.add(new ExtractedRowCoaDataAuditDto.Delete(
                                                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                        .getTableRowPkId().getTableId(),
                                                extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId()
                                                        .getTableRowPkId().getRowId().intValue(),
                                                ExtractedTableRowCoaDataJoinEntity.EXPLAINABILITY_ID_COL_NAME,
                                                extractedTableRowCoaDataJoinEntity.getExplainability().getId().toString()
                                        ));
                                    }
                                    return deleteAuditDtos.stream();
                                }
                        )
                        .toList();
        logger.debugf("Deleting row coa mappings for tables");
        //Delete mappings.
        long deleteCount = extractedRowCoaDataRepository.deleteMappedCoaDataForTables(tableIds);
        if (deleteCount != coaDataJoinEntitiesForTables.size()) {
            throw new DeletionFailedException("Failed to delete one or more coa data mapping for tables " + tableIds);
        }
        logger.debugf("Row coa mappings for tables deleted");
        logger.debugf("Auditing row coa mapping deletion for tables");
        exchangeService.auditExtractedRowCoaDataDelete(extractedRowCoaDataAuditDeleteDtos, isApiKeyAuthenticated);
        logger.debugf("Audits for row coa mapping deletion for tables created.");
        logger.debugf("Transaction completed for row coa mappings deletion for multiple tables");
    }

    public Optional<ExtractedTableRowCoaDataJoinEntity> findByRowOptional(
            Integer tableId, Integer rowId) {
        return extractedRowCoaDataRepository.findByRowOptional(tableId, rowId.shortValue());
    }

    public List<ExtractedTableRowCoaDataJoinEntity> findCoaDataMappingsForTable(List<Integer> tablesIds) {
        return extractedRowCoaDataRepository.findByTableIds(tablesIds);
    }

    public List<Integer> getTablesWithCoaMappingsInList(List<Integer> tableIds) {
        return extractedRowCoaDataRepository.findTableIdsWithCoaMappingsInList(tableIds);
    }
}
