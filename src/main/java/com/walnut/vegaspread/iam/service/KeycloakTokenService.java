package com.walnut.vegaspread.iam.service;

import com.walnut.vegaspread.iam.model.AuthDto;
import com.walnut.vegaspread.iam.model.TokenDto;
import jakarta.enterprise.context.ApplicationScoped;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.keycloak.admin.client.Keycloak;
import org.keycloak.admin.client.KeycloakBuilder;
import org.keycloak.representations.AccessTokenResponse;

/**
 * Service for Keycloak token operations.
 */
@ApplicationScoped
public class KeycloakTokenService {

    private final Logger logger = LogManager.getLogger(KeycloakTokenService.class);

    @ConfigProperty(name = "quarkus.keycloak.admin-client.server-url")
    String keycloakServerUrl;

    @ConfigProperty(name = "quarkus.keycloak.api-key-auth.realm")
    String apiKeyAuthRealm;

    @ConfigProperty(name = "vega.env", defaultValue = "dev")
    String envName;

    /**
     * Get a bearer token from Keycloak using client credentials
     *
     * @param auth The authentication DTO containing client_id and api_key
     * @return TokenDto containing the access token and related information
     */
    public TokenDto getToken(AuthDto auth) {

        try (Keycloak keycloak = KeycloakBuilder.builder()
                .serverUrl(keycloakServerUrl)
                .realm(apiKeyAuthRealm)
                .clientId(auth.clientId())
                .clientSecret(auth.apiKey())
                .grantType("client_credentials")
                .build()) {

            AccessTokenResponse tokenResponse = keycloak.tokenManager().grantToken();

            // Create a new TokenDto record with the token information
            return new TokenDto(tokenResponse.getToken());
        } catch (Exception e) {
            logger.error("Failed to get token from Keycloak", e);
            throw new RuntimeException("Failed to get token: " + e.getMessage());
        }
    }
}
