package com.walnut.vegaspread.common.utils;

public class ConfigKeys {
    public static final String ENV_NAME_KEY = "vega.env";
    public static final String CLOUD_PROVIDER_TYPE = "vegaspread.cloud.provider";
    public static final String AWS_GATEWAY_URL = "vegaspread.cloud.api-gateway-url";
    public static final String AWS_REGION = "aws.region";

    private ConfigKeys() {
        throw new IllegalStateException("Utility class");
    }
}
