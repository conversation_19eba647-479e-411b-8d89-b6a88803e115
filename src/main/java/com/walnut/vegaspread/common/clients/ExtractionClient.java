package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

import java.util.List;
import java.util.UUID;

@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@RegisterRestClient(configKey = "extraction")
@RegisterProvider(RestClientLoggingFilter.class)
@RegisterClientHeaders
public interface ExtractionClient {

    String LAYOUT_BLOCK_URL = "/block";

    @Path(LAYOUT_BLOCK_URL + "/doc/{docId}/ids")
    @GET
    List<Integer> getBlockIdsForDoc(@PathParam("docId") UUID docId);
}
