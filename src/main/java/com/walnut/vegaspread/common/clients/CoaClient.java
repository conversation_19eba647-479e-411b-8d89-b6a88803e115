package com.walnut.vegaspread.common.clients;

import com.walnut.vegaspread.common.clients.filter.RestClientLoggingFilter;
import com.walnut.vegaspread.common.model.coa.CoaItem;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.model.coa.CoaTaskDto;
import io.quarkus.runtime.annotations.RegisterForReflection;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.rest.client.annotation.RegisterClientHeaders;
import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;
import org.jboss.resteasy.reactive.RestQuery;

import java.util.List;

@RegisterForReflection
@RegisterRestClient(configKey = "coa")
@RegisterProvider(RestClientLoggingFilter.class)
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("")
@RegisterClientHeaders
public interface CoaClient {
    @Path("/task")
    @POST
    CoaTaskDto.NewTaskResponse createCoaTask(CoaTaskDto.NewTask coaTask);

    @Path("/list")
    @GET
    List<CoaItem> getCoaList(@RestQuery String clientName);

    @POST
    @Path("/get")
    List<CoaItemDto> get(List<Integer> coaIds);
}
