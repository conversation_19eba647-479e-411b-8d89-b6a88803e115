// 5. CloudProviderFactory
package com.walnut.vegaspread.workflow.cloud;

import com.walnut.vegaspread.common.utils.ConfigKeys;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Instance;
import org.eclipse.microprofile.config.inject.ConfigProperty;

@ApplicationScoped
public class CloudProviderFactory {

    Instance<CloudProvider> cloudProviders;
    String cloudProviderType;

    public CloudProviderFactory(Instance<CloudProvider> cloudProviders,
                                @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType) {
        this.cloudProviders = cloudProviders;
        this.cloudProviderType = cloudProviderType;
    }

    public CloudProvider getCloudProvider() {
        for (CloudProvider provider : cloudProviders) {
            if (provider.getClass().getSimpleName().toLowerCase().startsWith(cloudProviderType.toLowerCase())) {
                return provider;
            }
        }

        throw new IllegalStateException("No cloud provider configured for type: " + cloudProviderType);
    }
}