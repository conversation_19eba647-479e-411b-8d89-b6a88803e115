package com.walnut.vegaspread.workflow.cloud;

import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.enterprise.inject.Produces;

@ApplicationScoped
public class ApiKeyCredentialsProducer {

    private String clientId;
    private String apiKey;

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    @Produces
    public ApiKeyCredentialsDto produceApiKeyCredentialsDto() {
        return new ApiKeyCredentialsDto(clientId, apiKey);
    }
}