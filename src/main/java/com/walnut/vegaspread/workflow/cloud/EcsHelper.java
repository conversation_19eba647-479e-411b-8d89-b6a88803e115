package com.walnut.vegaspread.workflow.cloud;

import com.walnut.vegaspread.common.utils.ConfigKeys;
import io.quarkus.arc.profile.IfBuildProfile;
import jakarta.enterprise.context.ApplicationScoped;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.http.urlconnection.UrlConnectionHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.ecs.EcsClient;
import software.amazon.awssdk.services.ecs.model.DescribeTaskDefinitionRequest;
import software.amazon.awssdk.services.ecs.model.DescribeTaskDefinitionResponse;
import software.amazon.awssdk.services.ecs.model.ListTaskDefinitionsRequest;
import software.amazon.awssdk.services.ecs.model.SortOrder;

import java.util.List;
import java.util.Optional;

@ApplicationScoped
@IfBuildProfile("aws")
public class EcsHelper {
    private final EcsClient ecsClient;

    public EcsHelper(@ConfigProperty(name = ConfigKeys.AWS_REGION) Optional<String> region) {
        if (region.isEmpty()) {
            throw new IllegalStateException("AWS region is not configured");
        }
        this.ecsClient = EcsClient.builder()
                .region(Region.of(region.get()))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .httpClient(UrlConnectionHttpClient.create())
                .build();
    }

    public String getLatestRevisionForContainer(String familyName, String containerName) {

        ListTaskDefinitionsRequest listRequest = ListTaskDefinitionsRequest.builder()
                .familyPrefix(familyName)
                .sort(SortOrder.DESC)
                .build();

        List<String> taskDefinitionArns = ecsClient.listTaskDefinitions(listRequest).taskDefinitionArns();
        for (String taskDefArn : taskDefinitionArns) {
            DescribeTaskDefinitionRequest describeRequest = DescribeTaskDefinitionRequest.builder()
                    .taskDefinition(taskDefArn)
                    .build();
            DescribeTaskDefinitionResponse describeResponse = ecsClient.describeTaskDefinition(describeRequest);

            boolean containsContainer = describeResponse.taskDefinition().containerDefinitions().stream()
                    .anyMatch(container -> container.name().equals(containerName));

            if (containsContainer) {
                return describeResponse.taskDefinition().family() +
                        ":" + describeResponse.taskDefinition().revision();
            }
        }
        throw new IllegalStateException(
                "Could not find task definition for container: " + containerName + " in family: " + familyName);
    }
}
