package com.walnut.vegaspread.workflow.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRootName;
import io.quarkus.runtime.annotations.RegisterForReflection;

import java.util.List;

@RegisterForReflection
@JsonRootName("task")
public class EcsRunTaskDto {
    public String cluster;
    public String taskDefinition;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public String launchType;
    public NetworkConfiguration networkConfiguration;
    public Overrides overrides;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public List<Strategy> capacityProviderStrategy;

    public static class Strategy {
        public String capacityProvider;
        public Integer weight;
        public Integer base;
    }

    public static class NetworkConfiguration {
        public AwsVpcConfiguration awsvpcConfiguration;

        public static class AwsVpcConfiguration {
            public String assignPublicIp;
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public List<String> securityGroups;
            public List<String> subnets;
        }
    }

    public static class Overrides {
        public List<ContainerOverride> containerOverrides;

        public static class ContainerOverride {
            public String name;
            public List<String> command;
            public List<EnvironmentVariable> environment;

            public static class EnvironmentVariable {
                public String name;
                public String value;
            }
        }
    }
}
