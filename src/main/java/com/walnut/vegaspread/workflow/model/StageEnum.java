package com.walnut.vegaspread.workflow.model;

public interface StageEnum {

    enum Process {
        ROTATION,
        OCR,
        LAYOUT,
        TABLE,
        FS_CLF,
        PROCESS,
        FINISH_REVIEW,
        REPROCESS_COMPLETE,
        REPROCESS_TABLE_TAG,
        R<PERSON><PERSON>CESS_COA,
        REPROCESS_NTA_BLOCK
    }

    enum Reprocess {
        COMPLETE,
        TABLE_TAG,
        COA,
        NTA_BLOCK
    }
}
