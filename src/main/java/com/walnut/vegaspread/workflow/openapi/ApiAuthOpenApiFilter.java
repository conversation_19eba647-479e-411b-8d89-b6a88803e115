package com.walnut.vegaspread.workflow.openapi;

import io.quarkus.smallrye.openapi.OpenApiFilter;
import org.eclipse.microprofile.openapi.OASFactory;
import org.eclipse.microprofile.openapi.OASFilter;
import org.eclipse.microprofile.openapi.models.OpenAPI;
import org.eclipse.microprofile.openapi.models.media.Schema;
import org.eclipse.microprofile.openapi.models.parameters.Parameter;

import java.util.Collections;

@OpenApiFilter(OpenApiFilter.RunStage.BUILD)
public class ApiAuthOpenApiFilter implements OASFilter {
    private static final String TARGET_PATH_PREFIX = "/wise/processor";

    @Override
    public void filterOpenAPI(OpenAPI openAPI) {
        Parameter clientIdHeader = OASFactory.createParameter()
                .name("X-CLIENT-ID")
                .description("Client ID for authentication")
                .required(true)
                .in(Parameter.In.HEADER)
                .schema(OASFactory.createSchema().type(Collections.singletonList(Schema.SchemaType.STRING)));
        Parameter apiKeyHeader = OASFactory.createParameter()
                .name("X-API-KEY")
                .description("API KEY for authentication")
                .required(true)
                .in(Parameter.In.HEADER)
                .schema(OASFactory.createSchema().type(Collections.singletonList(Schema.SchemaType.STRING)));

        openAPI.getPaths().getPathItems().forEach((path, pathItem) -> {
            if (path.contains(TARGET_PATH_PREFIX)) {
                pathItem.getOperations().forEach(((httpMethod, operation) -> {
                    operation.addParameter(clientIdHeader);
                    operation.addParameter(apiKeyHeader);
                    pathItem.setOperation(httpMethod, operation);
                }));
            }
        });
    }
}
