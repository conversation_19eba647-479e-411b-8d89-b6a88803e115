package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.security.ApiKeyAuthenticate;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.cloud.ApiKeyCredentialsProducer;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.service.DocumentService;
import com.walnut.vegaspread.workflow.service.WiseWorkflowService;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.HeaderParam;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.config.inject.ConfigProperty;
import org.eclipse.microprofile.openapi.annotations.enums.ParameterIn;
import org.eclipse.microprofile.openapi.annotations.parameters.Parameter;
import org.jboss.logging.Logger;
import org.jboss.resteasy.reactive.RestResponse;

import java.net.URI;
import java.util.UUID;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/callback")
@ApiKeyAuthenticate
public class CallbackResource {
    private static final Logger logger = Logger.getLogger(CallbackResource.class);

    private final DocumentService documentService;
    private final CloudProviderFactory cloudProviderFactory;
    private final ApiKeyCredentialsProducer apiKeyCredentialsProducer;
    private final String envName;
    private final String cloudProviderType;

    public CallbackResource(DocumentService documentService,
                            CloudProviderFactory cloudProviderFactory,
                            ApiKeyCredentialsProducer apiKeyCredentialsProducer,
                            @ConfigProperty(name = ConfigKeys.CLOUD_PROVIDER_TYPE) String cloudProviderType,
                            @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY) String envName) {
        this.documentService = documentService;
        this.cloudProviderFactory = cloudProviderFactory;
        this.apiKeyCredentialsProducer = apiKeyCredentialsProducer;
        this.cloudProviderType = cloudProviderType;
        this.envName = envName;
    }

    @POST
    @Path("/{docId}")
    public RestResponse<Void> callback(@PathParam("docId") UUID docId,
                                       WiseCallbackDto.CbMetadata cbMetadata,
                                       @Parameter(name = "X-CLIENT-ID", in = ParameterIn.HEADER, required = true,
                                               description = "Client ID")
                                       @HeaderParam("X-CLIENT-ID") String clientId,
                                       @Parameter(name = "X-API-KEY", in = ParameterIn.HEADER, required = true,
                                               description = "API Key")
                                       @HeaderParam("X-API-KEY") String apiKey) {
        logger.debugf("Received callback for docId: %s, metadata: %s", docId, cbMetadata);
        logger.debugf("Api Key : %s", apiKey);
        logger.debugf("Cloud Provider Type : %s", cloudProviderType);
        logger.debugf("Environment Name : %s", envName);
        DocOutputDto document = documentService.updateDocStatus(
                cbMetadata.getDocId(),
                StatusEnum.PROCESSING,
                cbMetadata.getStatus().toString());

        if (document == null) {
            logger.errorf("Document not found for docId: %s", docId);
            return RestResponse.notFound();
        }

        // Get the appropriate cloud provider from the factory
        CloudProvider cloudProvider = cloudProviderFactory.getCloudProvider();

        URI serviceBaseUrl = cloudProvider.buildServiceBaseUrl();

        apiKeyCredentialsProducer.setClientId(clientId);
        apiKeyCredentialsProducer.setApiKey(apiKey);

        // Create workflow service with the cloud provider
        WiseWorkflowService wiseWorkflowService = new WiseWorkflowService(
                cbMetadata,
                apiKeyCredentialsProducer.produceApiKeyCredentialsDto(),
                serviceBaseUrl,
                cloudProvider,
                envName,
                document);

        wiseWorkflowService.handleCallback(cbMetadata.getStatus());
        return RestResponse.ok();
    }
}