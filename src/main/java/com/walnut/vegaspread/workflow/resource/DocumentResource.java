package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.entity.Review;
import com.walnut.vegaspread.workflow.model.DocFile;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.ListTaskDto;
import com.walnut.vegaspread.workflow.model.PutSignedLinkDto;
import com.walnut.vegaspread.workflow.model.TaskListResponseDto;
import com.walnut.vegaspread.workflow.model.UpdateReviewerDto;
import com.walnut.vegaspread.workflow.model.UpdateUiDocDto;
import com.walnut.vegaspread.workflow.model.UploadItemSchema;
import com.walnut.vegaspread.workflow.service.DocumentService;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.DELETE;
import jakarta.ws.rs.DefaultValue;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.PATCH;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.PUT;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.jwt.JsonWebToken;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import org.jboss.resteasy.reactive.RestForm;
import org.jboss.resteasy.reactive.RestQuery;
import org.jboss.resteasy.reactive.multipart.FileUpload;

import java.io.IOException;
import java.net.URI;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static com.walnut.vegaspread.common.utils.Jwt.getClientName;
import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/document")
@Authenticated
public class DocumentResource {

    private final DocumentService documentService;
    private final JsonWebToken jwt;
    private final CloudProviderFactory cloudProviderFactory;

    public DocumentResource(DocumentService documentService, JsonWebToken jwt,
                            CloudProviderFactory cloudProviderFactory) {
        this.documentService = documentService;
        this.jwt = jwt;
        this.cloudProviderFactory = cloudProviderFactory;
    }

    @Consumes(MediaType.MULTIPART_FORM_DATA)
    @Path("/upload")
    @PUT
    @RolesAllowed(Roles.UPLOAD_FILE)
    public DocOutputDto upload(@DefaultValue("0") @RestQuery Integer spreadId, @RestForm String applicationId,
                               @RestForm("file") @Schema(implementation = UploadItemSchema.class) FileUpload fileUpload) throws IOException {
        DocFile docFile = new DocFile(fileUpload.filePath(), fileUpload.fileName(), fileUpload.size());
        return documentService.docToOutput(
                documentService.uploadDoc(spreadId, docFile, getUsername(jwt), true, applicationId));
    }

    @Path("/upload-url")
    @RolesAllowed(Roles.UPLOAD_FILE)
    @GET
    public PutSignedLinkDto uploadLink(@DefaultValue("0") @RestQuery Integer spreadId, @RestQuery String fileName,
                                       @RestQuery Integer fileSize, @RestQuery String applicationId) {
        return documentService.generateUploadLinkForNewDoc(spreadId, fileName, fileSize, getUsername(jwt),
                applicationId);
    }

    @Path("/{docId}/time")
    @PATCH
    public LocalDateTime updateTime(@PathParam("docId") UUID docId) {
        return documentService.updateTimeInDb(docId, getUsername(jwt));
    }

    @Path("/{docId}/ui")
    @PATCH
    @RolesAllowed(Roles.MAP_COA)
    public DocOutputDto updateDocFromUi(@PathParam("docId") UUID docId, UpdateUiDocDto docDto) {
        CloudProvider cloudProvider = cloudProviderFactory.getCloudProvider();
        URI baseUri = cloudProvider.buildServiceBaseUrl();
        return documentService.docToOutput(
                documentService.updateInDbByUi(docId, docDto, getClientName(jwt), getUsername(jwt), baseUri));
    }

    @Path("/{docId}")
    @GET
    public DocOutputDto get(@PathParam("docId") UUID docId) {
        return documentService.docToOutput(documentService.getFromDb(docId));
    }

    @Path("/link/{docId}")
    @GET
    public UrlDto getLink(@PathParam("docId") UUID docId, @QueryParam("fileType") String fileType) {
        return documentService.getSignedUrl(docId, fileType);
    }

    @Produces(MediaType.APPLICATION_OCTET_STREAM)
    @Path("/download/{docId}")
    @GET
    public byte[] download(@PathParam("docId") UUID docId, @QueryParam("fileType") String fileType) {
        return documentService.download(docId, fileType);
    }

    @DELETE
    @Path("/{docId}")
    public long delete(@PathParam("docId") UUID docId) {
        return documentService.deleteDoc(docId);
    }

    @Path("/list")
    @POST
    public TaskListResponseDto list(ListTaskDto.GetTaskList taskListDto) {
        return documentService.listDocs(taskListDto, getClientName(jwt));
    }

    @Path("/{docId}/review/request-changes")
    @POST
    @RolesAllowed(Roles.CHECKER_LVL_2)
    public DocOutputDto reviewChangesRequested(@PathParam("docId") UUID docId) {
        return documentService.requestReview(docId, getUsername(jwt));
    }

    @Path("/{docId}/review/approve")
    @POST
    @RolesAllowed(Roles.CHECKER_LVL_2)
    public DocOutputDto reviewApproved(@PathParam("docId") UUID docId) {
        return documentService.approveReview(docId, getUsername(jwt));
    }

    @Path("/{docId}/review/revise")
    @POST
    @RolesAllowed(Roles.CHECKER_LVL_2)
    public DocOutputDto reviewRevise(@PathParam("docId") UUID docId) {
        return documentService.reviseReview(docId, getUsername(jwt));
    }

    @Path("{docId}/review/send-for-approval")
    @POST
    @RolesAllowed(Roles.CHECKER_LVL_1)
    public DocOutputDto sendForLvl2Approval(@PathParam("docId") UUID docId, @RestQuery String lvl2Reviewer) {
        return documentService.requestApproval(docId, lvl2Reviewer, getUsername(jwt));
    }

    @Path("{docId}/review/reassign")
    @POST
    @RolesAllowed(Roles.CHECKER_LVL_1)
    public Review assignToAnotherReviewer(@PathParam("docId") UUID docId, UpdateReviewerDto updateReviewerDto) {
        return documentService.reassignReviewer(docId, updateReviewerDto, getUsername(jwt));
    }

    @Path("{docId}/reviews")
    @GET
    public List<Review> getAllReviews(@PathParam("docId") UUID docId) {
        return documentService.listReviews(docId);
    }
}
