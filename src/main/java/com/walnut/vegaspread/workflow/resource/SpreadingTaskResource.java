package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.model.CreateTaskDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.SpreadOutputDto;
import com.walnut.vegaspread.workflow.service.TaskService;
import io.quarkus.security.Authenticated;
import jakarta.annotation.security.RolesAllowed;
import jakarta.ws.rs.Consumes;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.PathParam;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.core.MediaType;
import org.eclipse.microprofile.jwt.JsonWebToken;

import static com.walnut.vegaspread.common.utils.Jwt.getClientName;
import static com.walnut.vegaspread.common.utils.Jwt.getUsername;

@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@Path("/spread")
@Authenticated
public class SpreadingTaskResource {

    private final JsonWebToken accessToken;
    private final TaskService taskService;

    public SpreadingTaskResource(JsonWebToken accessToken, TaskService taskService) {
        this.accessToken = accessToken;
        this.taskService = taskService;
    }

    @POST
    @RolesAllowed(Roles.UPLOAD_FILE)
    public DocOutputDto create(CreateTaskDto taskDto) {
        return taskService.taskToOutput(
                taskService.create(taskDto, getClientName(accessToken), getUsername(accessToken)));
    }

    @Path("/{spreadId}")
    @GET
    public SpreadOutputDto get(@PathParam("spreadId") Integer spreadId) {
        return taskService.taskToOutputList(taskService.get(spreadId));
    }
}
