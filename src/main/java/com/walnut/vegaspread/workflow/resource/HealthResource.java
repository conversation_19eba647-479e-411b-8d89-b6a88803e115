package com.walnut.vegaspread.workflow.resource;

import io.quarkus.security.Authenticated;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.core.Context;
import jakarta.ws.rs.core.UriInfo;

import java.net.URI;

@Path("/health")
public class HealthResource {

    @GET
    @Path("/ready")
    public String ready() {
        return "OK";
    }

    @GET
    @Path("/base-url")
    public URI baseUrl(@Context UriInfo uriInfo) {
        return uriInfo.getBaseUri();
    }

    @Authenticated
    @GET
    @Path("/authenticated")
    public String authenticated() {
        return "OK";
    }
}
