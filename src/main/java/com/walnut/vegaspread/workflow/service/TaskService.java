package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.CreateTaskDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.MetadataDto;
import com.walnut.vegaspread.workflow.model.SpreadOutputDto;
import com.walnut.vegaspread.workflow.repository.SpreadingTaskRepository;
import com.walnut.vegaspread.workflow.utils.OutputFormatter;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.transaction.Transactional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@ApplicationScoped
public class TaskService {
    private static final String ALL_FILTER = "all";
    private static final Logger logger = LogManager.getLogger(TaskService.class);
    private static final List<String> FILTER_BY_COLUMN_NAMES = Arrays.asList(
            SpreadingTask.ENTITY_NAME_FOREIGN_KEY_COL_NAME, SpreadingTask.INDUSTRY_FOREIGN_KEY_COL_NAME,
            SpreadingTask.REGION_FOREIGN_KEY_COL_NAME, ALL_FILTER);
    private static final Map<String, String> FILTER_BY_COLUMN_MAPPING = Map.of(
            SpreadingTask.ENTITY_NAME_FOREIGN_KEY_COL_NAME, "entityName.entityId",
            SpreadingTask.INDUSTRY_FOREIGN_KEY_COL_NAME, "industry.industryId",
            SpreadingTask.REGION_FOREIGN_KEY_COL_NAME, "region.regionId",
            ALL_FILTER, ALL_FILTER);

    private final SpreadingTaskRepository spreadingTaskRepository;
    private final DocumentService documentService;
    private final OutputFormatter outputFormatter;
    private final MetadataService metadataService;

    public TaskService(SpreadingTaskRepository spreadingTaskRepository, DocumentService documentService,
                       ExchangeService exchangeService, MetadataService metadataService) {
        this.spreadingTaskRepository = spreadingTaskRepository;
        this.documentService = documentService;
        this.outputFormatter = new OutputFormatter(exchangeService);
        this.metadataService = metadataService;
    }

    public DocOutputDto taskToOutput(SpreadingTask task) {
        return outputFormatter.taskToOutput(task.getDocuments().get(0), task);
    }

    public SpreadOutputDto taskToOutputList(SpreadingTask task) {
        return outputFormatter.taskToOutput(task.getDocuments(), task);
    }

    @Transactional
    public SpreadingTask create(CreateTaskDto taskDto, String clientName, String username) {
        Document doc = documentService.getFromDb(taskDto.docId());
        doc.setPeriod(taskDto.period());
        doc.setSpreadLevel(taskDto.spreadLevel());
        doc.setIsDigital(taskDto.isDigital());
        doc.setStatus(StatusEnum.CREATED);
        doc.setStatusText(StatusEnum.CREATED.toString());
        doc.setFileDenomination(taskDto.fileDenomination());
        doc.setOutputDenomination(taskDto.outputDenomination());
        doc.setLastModifiedBy(username);
        doc.setLastModifiedTime(LocalDateTime.now());

        SpreadingTask task = new SpreadingTask();
        task.setClientName(clientName);

        task.setEntityName(metadataService.getOrCreateEntity(taskDto.entityName(), username));
        task.setIndustry(metadataService.getOrCreateIndustry(taskDto.industry(), username));
        task.setRegion(metadataService.getOrCreateRegion(taskDto.region(), username));

        task.setAuditable(new Auditable(username));
        task.setLastModifiedBy(username);
        task.setLastModifiedTime(task.getAuditable().getCreatedTime());

        spreadingTaskRepository.persist(task);
        doc.setSpreadingTask(task);
        documentService.updateInDb(doc);
        task.getDocuments().add(doc);
        return task;
    }

    @Transactional
    public SpreadingTask createTaskForUpdateInDbByUi(MetadataDto.Entity entityName, MetadataDto.Industry industry,
                                                     MetadataDto.Region region, String clientName, String username,
                                                     Document doc) {
        SpreadingTask task = new SpreadingTask();
        EntityName entityForTask = metadataService.getOrCreateEntity(entityName, username);
        Industry industryForTask = metadataService.getOrCreateIndustry(industry, username);
        Region regionForTask = metadataService.getOrCreateRegion(region, username);

        Optional<SpreadingTask> optSpreadingTask =
                spreadingTaskRepository.findByEntityNameAndIndustryNameAndRegionNameOptional(
                        entityForTask.getEntityId(),
                        industryForTask.getIndustryId(), regionForTask.getRegionId());
        if (optSpreadingTask.isPresent()) {
            task = optSpreadingTask.get();
        } else {
            task.setClientName(clientName);
            task.setEntityName(entityForTask);
            task.setIndustry(industryForTask);
            task.setRegion(regionForTask);
            task.setAuditable(new Auditable(username));
            task.setLastModifiedBy(username);
            task.setLastModifiedTime(task.getAuditable().getCreatedTime());
        }
        spreadingTaskRepository.persist(task);
        task.getDocuments().add(doc);
        return task;
    }

    public SpreadingTask get(Integer spreadId) {
        return spreadingTaskRepository.findById(spreadId);
    }

    public List<Integer> filterSpreadingTask(String filterColumn, Integer filterValue, String clientName) {
        if (!isValidFilterColumn(filterColumn)) {
            logger.error("Invalid column name: {}", filterColumn);
            return List.of();
        }
        return spreadingTaskRepository.filterTask(FILTER_BY_COLUMN_MAPPING.get(filterColumn), filterValue, clientName);
    }

    private boolean isValidFilterColumn(String columnName) {
        if (columnName == null || columnName.isBlank()) {
            return false;
        }
        return FILTER_BY_COLUMN_NAMES.contains(columnName);
    }
}
