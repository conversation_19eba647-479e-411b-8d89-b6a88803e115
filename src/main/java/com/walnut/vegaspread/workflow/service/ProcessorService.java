package com.walnut.vegaspread.workflow.service;

import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.utils.ConfigKeys;
import com.walnut.vegaspread.workflow.cloud.CloudProvider;
import com.walnut.vegaspread.workflow.cloud.CloudProviderFactory;
import com.walnut.vegaspread.workflow.model.ApiKeyCredentialsDto;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.model.WiseCallbackDto;
import com.walnut.vegaspread.workflow.utils.Config;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.ws.rs.core.Response;
import org.eclipse.microprofile.config.inject.ConfigProperty;

import java.net.URI;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

@ApplicationScoped
public class ProcessorService {
    private final DocumentService documentService;
    private final ExchangeService exchangeService;
    private final CloudProvider cloudProvider;

    @ConfigProperty(name = ConfigKeys.ENV_NAME_KEY)
    String envName;
    @ConfigProperty(name = Config.WISE_CLIENT_SECRET_KEY)
    String clientSecret;
    @ConfigProperty(name = Config.API_CLIENT_NAME)
    String clientName;

    public ProcessorService(DocumentService documentService, ExchangeService exchangeService,
                            CloudProviderFactory cloudProviderFactory) {
        this.documentService = documentService;
        this.exchangeService = exchangeService;
        this.cloudProvider = cloudProviderFactory.getCloudProvider();
    }

    private static WiseCallbackDto.CbStatus getCbStatus(StageEnum.Process processStageEnum) {
        WiseCallbackDto.CbStatus status;
        switch (processStageEnum) {
            case ROTATION -> status = WiseCallbackDto.CbStatus.START_ROTATION;
            case OCR -> status = WiseCallbackDto.CbStatus.START_OCR;
            case LAYOUT, TABLE -> status = WiseCallbackDto.CbStatus.START_DOCUMENT_AI;
            case FS_CLF -> status = WiseCallbackDto.CbStatus.START_FS_CLF;
            case PROCESS, REPROCESS_COA, REPROCESS_COMPLETE, REPROCESS_TABLE_TAG, REPROCESS_NTA_BLOCK ->
                    status = WiseCallbackDto.CbStatus.START_PROCESSOR;
            case FINISH_REVIEW -> status = WiseCallbackDto.CbStatus.FINISH_REVIEW;
            default -> throw new IllegalStateException("Unexpected value: " + processStageEnum);
        }
        return status;
    }

    public Response.Status startProcess(UUID docId, StageEnum.Process processStageEnum, URI baseUri) {
        DocOutputDto doc;
        if (processStageEnum.equals(StageEnum.Process.FINISH_REVIEW)) {
            doc = documentService.docToOutput(documentService.getFromDb(docId));
        } else {
            doc = documentService.updateDocStatus(docId, StatusEnum.PROCESSING, processStageEnum.toString());
        }
        if (doc == null) {
            return Response.Status.NOT_FOUND;
        }

        WiseCallbackDto.CbStatus status = getCbStatus(processStageEnum);
        if (status == null) {
            status = WiseCallbackDto.CbStatus.START_ROTATION;
        }
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, status);
        wiseWorkflowService.startProcess(processStageEnum);
        if (!status.equals(WiseCallbackDto.CbStatus.START_ROTATION)) {
            exchangeService.createDocAudit(
                    List.of(new DocumentAuditDto.Create(docId, "PROCESSING", processStageEnum.toString())));
        }
        return Response.Status.OK;
    }

    public Response.Status processBlock(UUID docId, URI baseUri, Integer blockId) throws InterruptedException {
        DocOutputDto doc = documentService.docToOutput(documentService.getFromDb(docId));
        WiseCallbackDto.CbStatus status = getCbStatus(StageEnum.Process.REPROCESS_NTA_BLOCK);
        WiseWorkflowService wiseWorkflowService = getWiseWorkflowService(baseUri, doc, status);
        wiseWorkflowService.startReprocessBlock(blockId);
        exchangeService.createDocAudit(
                List.of(new DocumentAuditDto.Create(docId, "PROCESSING_BLOCK", blockId.toString())));
        return Response.Status.OK;
    }

    private WiseWorkflowService getWiseWorkflowService(URI baseUri, DocOutputDto doc, WiseCallbackDto.CbStatus status) {
        String bucketName = cloudProvider.getBucketName();
        WiseCallbackDto.CbMetadata cbMetadata = new WiseCallbackDto.CbMetadata(doc.docId(),
                doc.createdTime().toLocalDate(), bucketName, Objects.equals(doc.documentType(), "Digital"), status);

        ApiKeyCredentialsDto credentialsDto = new ApiKeyCredentialsDto(this.clientName, this.clientSecret);
        return new WiseWorkflowService(cbMetadata, credentialsDto, baseUri, cloudProvider, this.envName, doc);
    }
}
