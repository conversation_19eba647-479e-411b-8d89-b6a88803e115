package com.walnut.vegaspread.workflow.repository;

import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.model.JpaReflection;
import io.quarkus.hibernate.orm.panache.PanacheRepositoryBase;
import io.quarkus.panache.common.Sort;
import jakarta.enterprise.context.ApplicationScoped;

import java.util.List;
import java.util.UUID;

@ApplicationScoped
public class DocumentRepository implements PanacheRepositoryBase<Document, UUID> {

    public List<Document> findByIds(List<UUID> docIds, Sort sort) {
        return find("docId in ?1", sort, docIds).list();
    }

    public List<UUID> findCompletedDocIdsBySpreadIds(List<Integer> spreadIds) {
        return find("spreadingTask.spreadId in ?1 AND status = ?2 AND isOutlier = False", spreadIds,
                StatusEnum.COMPLETED).project(JpaReflection.DocIdOnly.class)
                .stream()
                .map(JpaReflection.DocIdOnly::getDocId)
                .toList();
    }
}
