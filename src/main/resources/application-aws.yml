quarkus:
  arc:
    exclude-types: com.walnut.vegaspread.common.security.AwsIamDbCredentialsProvider

  oidc:
    tls:
      tls-configuration-name: vega-tls

  tls:
    vega-tls:
      trust-store:
        p12:
          path: ssl/rcbc-truststore.p12

  native:
    resources:
      includes: ssl/rcbc-truststore.p12

  keycloak:
    admin-client:
      server-url: ${KEYCLOAK_BASE_URL}
      client-id: admin-cli
      grant-type: CLIENT_CREDENTIALS
      tls-configuration-name: vega-tls

vegaspread:
  cloud:
    provider: aws
    api-gateway-url: ${API_GATEWAY_URL}
    region: ${AURORA_INSTANCE_REGION:ap-southeast-1}
