quarkus:
  datasource:
    db-kind: h2
    jdbc:
      url: jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;MODE=MySQL
      driver: org.h2.Driver
      transactions: enabled
    username: test
    password: test

  keycloak:
    devservices:
      enabled: false

  oidc:
    auth-server-url: http://localhost:8180/realms/myrealm
    client-id: my-client

  hibernate-orm:
    log:
      sql: false
      bind-parameters: false

  rest-client:
    runpod:
      url: http://runpod.url
    ezee-callback:
      url: http://localhost:8080

  google:
    cloud:
      service-account-location: C:\Users\<USER>\OneDrive\The Walnut AI\keys\envs\vegaspread-7586a-1187b3c5758e.json

  arc:
    exclude-types: com.walnut.vegaspread.common.security.ApiKeyInterceptor

wise:
  client:
    secret: client-secret

vega:
  env: test
  api-client-name: test-api-client

vegaspread:
  cloud:
    provider: gcp
    bucket-name: test-bucket
