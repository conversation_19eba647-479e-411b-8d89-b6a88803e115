quarkus:
  datasource:
    jdbc:
      additional-jdbc-properties:
        cloudSqlInstance: ${CLOUD_SQL_INSTANCE}
        socketFactory: com.google.cloud.sql.mysql.SocketFactory
        enableIamAuth: true
      url: jdbc:mysql:///${vega.env}_vega_audit
    username: ${vega.env}-vegaspread

  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  http:
    root-path: /vegaspread/api/v1/audit
    cors:
      ~: true
      origins: "*"
    access-log:
      enabled: true

  flyway:
    migrate-at-start: true
    placeholders:
      audit: ${vega.env}_vega_audit
      coa: ${vega.env}_vega_coa
      extraction: ${vega.env}_vega_extraction
      workflow: ${vega.env}_vega_workflow

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization

vega:
  env: ${VEGA_ENV}
