quarkus:
  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  http:
    cors:
      enabled: true
      origins: "*"
    access-log:
      enabled: true
    root-path: /vegaspread/api/v1/iam
    proxy:
      proxy-address-forwarding: true
      enable-forwarded-host: true
      enable-forwarded-prefix: true
    limits:
      max-form-attribute-size: 10K

vega:
  env: ${VEGA_ENV}
