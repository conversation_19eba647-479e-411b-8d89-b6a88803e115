quarkus:
  datasource:
    jdbc:
      additional-jdbc-properties:
        cloudSqlInstance: ${CLOUD_SQL_INSTANCE}
        socketFactory: com.google.cloud.sql.mysql.SocketFactory
        enableIamAuth: true
      url: jdbc:mysql:///${vega.env}_vega_extraction
    username: ${vega.env}-vegaspread

  native:
    resources:
      includes: clientSummaryTemplate/jsw_output.json

  smallrye-openapi:
    security-scheme: oauth2-implicit

  swagger-ui:
    always-include: true
    oauth-client-id: workflow-swagger-ui

  http:
    root-path: /vegaspread/api/v1/extraction
    cors:
      ~: true
      origins: "*"
    access-log:
      enabled: true

  flyway:
    migrate-at-start: true

  hibernate-orm:
    physical-naming-strategy: org.hibernate.boot.model.naming.CamelCaseToUnderscoresNamingStrategy
    log:
      sql: false

org:
  eclipse:
    microprofile:
      rest:
        client:
          propagateHeaders: Authorization,X-CLIENT-ID,X-API-KEY

vega:
  env: ${VEGA_ENV}
