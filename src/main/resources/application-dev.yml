quarkus:
  datasource:
    username: root
    password: root
    jdbc:
      url: *********************************************

  live-reload:
    enabled: true

  oidc:
    auth-server-url: https://auth.vegaspread.cloud/realms/dev-vega

  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth

  rest-client:
    runpod:
      url: https://api.runpod.ai/v2/7a31shtmkg3sa0

wise:
  client:
    secret: client-secret

vega:
  env: dev
  
vegaspread:
  cloud:
    provider: gcp
