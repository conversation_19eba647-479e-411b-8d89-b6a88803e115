quarkus:
  flyway:
    placeholders:
      audit: dev_vega_audit
      coa: dev_vega_coa
      extraction: dev_vega_extraction
      workflow: dev_vega_workflow
  hibernate-orm:
    log:
      sql: true
      bind-parameters: true
  datasource:
    username: root
    password: root
    jdbc:
      url: ******************************************

  oidc:
    auth-server-url: https://auth.vegaspread.cloud/realms/dev-vega

  smallrye-openapi:
    oauth2-implicit-authorization-url: https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth

  http:
    port: 8082

vega:
  env: dev
