package com.walnut.vegaspread.coa.resource;

import com.walnut.vegaspread.coa.entity.CoaEntity;
import com.walnut.vegaspread.coa.model.CoaEntityDto;
import com.walnut.vegaspread.coa.model.CoaListDto;
import com.walnut.vegaspread.coa.model.CoaUploadBean;
import com.walnut.vegaspread.coa.model.SortDto;
import com.walnut.vegaspread.coa.repository.CoaRepository;
import com.walnut.vegaspread.coa.service.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.coa.CoaItemDto;
import com.walnut.vegaspread.common.roles.Roles;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.MediaType;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.coa.resource.CommonTestUtils.CLIENT_NAME;
import static com.walnut.vegaspread.coa.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(CoaResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaResourceTest {

    private static final Logger log = LoggerFactory.getLogger(CoaResourceTest.class);
    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    CoaRepository coaRepository;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<CoaEntity> addCoaEntities() {
        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("coaText" + id)
                .clientName(CLIENT_NAME)
                .coaDescription("coaDesc" + id)
                .lvl1Category("category" + id)
                .isActive(id % 2 == 0)
                .sign(id % 2 == 0)
                .build()).toList();

        coaRepository.persist(coaEntities);
        return coaEntities;
    }

    @Transactional
    List<CoaEntityDto.Create> createCoaItemDtos() {
        List<CoaEntityDto.Create> coaEntityDtos = new ArrayList<>();
        CoaEntityDto.Create dto1 = new CoaEntityDto.Create("coaText1", "coaDesc1", "category1", Boolean.TRUE,
                Boolean.TRUE);
        CoaEntityDto.Create dto2 = new CoaEntityDto.Create("coaText2", "coaDesc2", "category2", Boolean.TRUE,
                Boolean.TRUE);
        CoaEntityDto.Create dto3 = new CoaEntityDto.Create("coaText3", "coaDesc3", "category3", Boolean.TRUE,
                Boolean.TRUE);
        coaEntityDtos.add(dto1);
        coaEntityDtos.add(dto2);
        coaEntityDtos.add(dto3);
        return coaEntityDtos;
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.CREATE_NEW_COA)
    void testCreate() {
        List<CoaEntityDto.Create> coaEntityDtos = createCoaItemDtos();
        List<CoaEntity> insertedEntities = given()
                .contentType(ContentType.JSON).queryParam("clientName", CLIENT_NAME).body(coaEntityDtos)
                .when().post("/create")
                .then().statusCode(200)
                .extract()
                .body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(coaEntityDtos.size(), insertedEntities.size());

        for (int i = 0; i < insertedEntities.size(); i++) {
            CoaEntity entity = insertedEntities.get(i);
            CoaEntityDto.Create dto = coaEntityDtos.get(i);
            Assertions.assertEquals(i + 1, entity.coaId);
            Assertions.assertEquals(dto.coaText(), entity.coaText);
            Assertions.assertEquals(dto.coaDescription(), entity.coaDescription);
            Assertions.assertEquals(CLIENT_NAME, entity.clientName);
            Assertions.assertEquals(dto.lvl1Category(), entity.lvl1Category);
            Assertions.assertEquals(dto.isActive(), entity.isActive);
        }
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithNullDtos() {
        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON)
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(updatedEntities.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithEmptyDtoList() {
        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(Collections.emptyList())
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(updatedEntities.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithInvalidCoaId() {
        // Reset the mock before the test
        Mockito.reset(exchangeService);
        addCoaEntities();

        CoaEntityDto.Update coaEntityUpdate = new CoaEntityDto.Update(100, Optional.of("Invalid COA Id"),
                Optional.of("Invalid COA id"), Optional.of("category1"), Optional.of(true), Optional.of(true));
        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        coaEntityUpdateDtos.add(coaEntityUpdate);

        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(coaEntityUpdateDtos)
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(updatedEntities.isEmpty());
        verify(exchangeService, times(1)).coaItemAuditForUpdate(Collections.emptyList());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdate() {
        int coaId = 1;
        List<CoaEntity> coaEntities = addCoaEntities();
        CoaEntity coaEntityToUpdate = coaEntities.get(coaId - 1);

        CoaEntityDto.Update coaEntityUpdate = new CoaEntityDto.Update(coaId, Optional.of("New COA Text"),
                Optional.of("New COA Description"), Optional.of("New Category"), Optional.of(true), Optional.of(true));
        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        coaEntityUpdateDtos.add(coaEntityUpdate);

        List<CoaItemAuditDto.Update> coaItemAudits = new ArrayList<>();
        CoaItemAuditDto.Update coaItemAudit1 = new CoaItemAuditDto.Update(coaId, CoaEntity.COA_TEXT_COL_NAME,
                coaEntityToUpdate.coaText, coaEntityUpdate.coaText().get());
        coaItemAudits.add(coaItemAudit1);
        CoaItemAuditDto.Update coaItemAudit2 = new CoaItemAuditDto.Update(coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                coaEntityToUpdate.isActive.toString(), coaEntityUpdate.isActive().get().toString());
        coaItemAudits.add(coaItemAudit2);
        CoaItemAuditDto.Update coaItemAudit3 = new CoaItemAuditDto.Update(coaId, CoaEntity.SIGN_COL_NAME,
                coaEntityToUpdate.getSign().toString(), coaEntityUpdate.sign().get().toString());
        coaItemAudits.add(coaItemAudit3);

        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(coaEntityUpdateDtos)
                .when().patch("/update")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        verify(exchangeService, times(1)).coaItemAuditForUpdate(coaItemAudits);
        CoaEntity coaEntity = updatedEntities.get(0);

        Assertions.assertEquals(coaEntityUpdate.coaId(), coaEntity.coaId);
        Assertions.assertEquals(coaEntityUpdate.coaText().get(), coaEntity.coaText);
        Assertions.assertEquals(coaEntityUpdate.coaDescription().get(), coaEntity.coaDescription);
        Assertions.assertEquals(coaEntityUpdate.lvl1Category().get(), coaEntity.lvl1Category);
        Assertions.assertEquals(coaEntityUpdate.isActive().get(), coaEntity.isActive);
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.SUPERADMIN)
    void testUpdateWithNullFields() {
        int coaId = 1;
        List<CoaEntity> coaEntities = addCoaEntities();
        CoaEntity coaEntityToUpdate = coaEntities.get(coaId - 1);

        List<CoaEntityDto.Update> coaEntityUpdateDtos = new ArrayList<>();
        CoaEntityDto.Update coaEntityUpdate = new CoaEntityDto.Update(coaId, null,
                null, null, null, null);
        coaEntityUpdateDtos.add(coaEntityUpdate);

        List<CoaEntity> updatedEntities = given()
                .contentType(ContentType.JSON).body(coaEntityUpdateDtos)
                .when().patch("/update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        verify(exchangeService, times(1)).coaItemAuditForUpdate(Collections.emptyList());
        CoaEntity updatedCoaEntity = updatedEntities.get(0);

        Assertions.assertEquals(coaEntityToUpdate.coaId, updatedCoaEntity.coaId);
        Assertions.assertEquals(coaEntityToUpdate.coaText, updatedCoaEntity.coaText);
        Assertions.assertEquals(coaEntityToUpdate.coaDescription, updatedCoaEntity.coaDescription);
        Assertions.assertEquals(coaEntityToUpdate.lvl1Category, updatedCoaEntity.lvl1Category);
        Assertions.assertEquals(coaEntityToUpdate.isActive, updatedCoaEntity.isActive);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListActive() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaEntity> activeCoaEntites = coaEntities.stream().filter(CoaEntity::getIsActive).toList();

        List<CoaEntity> entityList = given()
                .queryParam("clientName", CLIENT_NAME)
                .when().get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertIterableEquals(activeCoaEntites, entityList);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListNotActive() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaEntity> entityList = given()
                .when()
                .queryParam("clientName", CLIENT_NAME)
                .queryParam("onlyActive", false)
                .get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertIterableEquals(coaEntities, entityList);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListWithDefaultClientName() {
        List<CoaEntity> coaEntities = addCoaEntities();
        List<CoaEntity> defaultClientCoaEntities = coaEntities.stream()
                .filter(coaEntity -> coaEntity.getClientName().equals("walnut"))
                .toList();

        List<CoaEntity> defaultClientCoaList = given()
                .when()
                .get("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<List<CoaEntity>>() {
                });

        Assertions.assertIterableEquals(defaultClientCoaEntities, defaultClientCoaList);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListWithSortAndSearchForAllCoa() {
        List<CoaEntity> coaEntities = addCoaEntities();

        String searchString = "1";
        SortDto sortDto = new SortDto("coaText", "DESC");
        CoaListDto.GetSortAndSearchCoaList getSortAndSearchCoaList = new CoaListDto.GetSortAndSearchCoaList(
                List.of(sortDto), searchString);

        List<CoaEntity> listEntities = given()
                .contentType(ContentType.JSON)
                .queryParam("clientName", CLIENT_NAME)
                .body(getSortAndSearchCoaList)
                .when()
                .queryParam("onlyActive", false)
                .post("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<CoaEntity> filteredAndSortedEntities = coaEntities.stream()
                .filter(coaEntity -> (coaEntity.coaText.contains(searchString) || coaEntity.coaDescription.contains(
                        searchString)))
                .sorted(Comparator.comparing(CoaEntity::getCoaText).reversed()).toList();

        Assertions.assertIterableEquals(filteredAndSortedEntities, listEntities);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListWithSortAndSearchWithOnlyActiveCoa() {
        List<CoaEntity> coaEntities = addCoaEntities();

        String searchString = "1";
        SortDto sortDto = new SortDto("coaText", "DESC");
        CoaListDto.GetSortAndSearchCoaList getSortAndSearchCoaList = new CoaListDto.GetSortAndSearchCoaList(
                List.of(sortDto), searchString);

        List<CoaEntity> listEntities = given()
                .contentType(ContentType.JSON)
                .queryParam("clientName", CLIENT_NAME)
                .body(getSortAndSearchCoaList)
                .when()
                .post("/list")
                .then().statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<CoaEntity> filteredAndSortedEntities = coaEntities.stream()
                .filter(coaEntity -> (coaEntity.coaText.contains(searchString) || coaEntity.coaDescription.contains(
                        searchString)) && coaEntity.isActive)
                .sorted(Comparator.comparing(CoaEntity::getCoaText).reversed()).toList();

        Assertions.assertIterableEquals(filteredAndSortedEntities, listEntities);
    }

    @Test
    @TestSecurity(user = NAME)
    void testUpload() {
        File file = new File("src/test/resources/CoaResourceUpload.csv");
        List<CoaEntity> coaEntities = IntStream.range(1, 11).mapToObj(id -> CoaEntity.builder()
                .coaText("coaText" + id)
                .clientName(CLIENT_NAME)
                .coaDescription("coaDesc" + id)
                .lvl1Category("category" + id)
                .isActive(id % 2 == 0)
                .sign(id % 2 == 0)
                .build()).toList();

        List<CoaUploadBean> insertedEntities = given()
                .multiPart("coa-file", file)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .when().put("upload")
                .then().statusCode(200)
                .extract()
                .body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(coaEntities.size(), insertedEntities.size());
        for (int i = 0; i < insertedEntities.size(); i++) {
            CoaEntity coaEntity = coaEntities.get(i);
            CoaUploadBean insertedEntity = insertedEntities.get(i);
            Assertions.assertEquals(coaEntity.coaText, insertedEntity.getCoaText());
            Assertions.assertEquals(coaEntity.coaDescription, insertedEntity.getCoaDescription());
            Assertions.assertEquals(coaEntity.isActive, insertedEntity.getIsActive());
            Assertions.assertEquals(coaEntity.clientName, insertedEntity.getClientName());
            Assertions.assertEquals(coaEntity.lvl1Category, insertedEntity.getLvl1Category());
            Assertions.assertEquals(coaEntity.getSign(), insertedEntity.getSign());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testUpdateUpload() {
        File file = new File("src/test/resources/CoaResourceUpdateUpload.csv");

        addCoaEntities();

        List<CoaEntity> updatedEntities = given()
                .multiPart("coa-file", file)
                .contentType(MediaType.MULTIPART_FORM_DATA)
                .when().put("/update/upload")
                .then().statusCode(200)
                .extract()
                .body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(2, updatedEntities.size());

        CoaEntity updatedEntity = coaRepository.findById(1);
        Assertions.assertEquals(1, updatedEntity.getCoaId());
        Assertions.assertEquals("coaNewText1", updatedEntity.getCoaText());
        Assertions.assertEquals("coaDesc1", updatedEntity.getCoaDescription());
        Assertions.assertEquals(true, updatedEntity.getIsActive());
        Assertions.assertEquals("category1", updatedEntity.getLvl1Category());

        CoaEntity updatedEntity2 = coaRepository.findById(2);
        Assertions.assertEquals(2, updatedEntity2.getCoaId());
        Assertions.assertEquals("coaText2", updatedEntity2.getCoaText());
        Assertions.assertEquals("coaNewDesc2", updatedEntity2.getCoaDescription());
        Assertions.assertEquals(false, updatedEntity2.getIsActive());
        Assertions.assertEquals("category2", updatedEntity2.getLvl1Category());
    }

    @Test
    @TestSecurity(user = NAME, roles = Roles.ADMIN)
    void testDelete() {

        List<CoaEntity> coaEntities = addCoaEntities();
        CoaEntity entityToDelete = coaEntities.get(1);

        List<CoaItemAuditDto.Delete> coaItemRequests = List.of(
                new CoaItemAuditDto.Delete(entityToDelete.coaId, CoaEntity.COA_TEXT_COL_NAME, entityToDelete.coaText),
                new CoaItemAuditDto.Delete(entityToDelete.coaId, CoaEntity.IS_ACTIVE_COL_NAME,
                        entityToDelete.isActive.toString())
        );

        given()
                .when().delete("/{coaId}", entityToDelete.coaId)
                .then().statusCode(204);

        verify(exchangeService, times(1)).coaItemAuditForDelete(coaItemRequests);
        Assertions.assertFalse(coaRepository.findById(entityToDelete.coaId).isActive);
    }

    @Test
    @TestSecurity(user = NAME)
    void testGet() {
        List<CoaEntity> coaEntities = addCoaEntities();

        List<CoaEntity> coaEntitiesCopy = new ArrayList<>(coaEntities);
        Collections.shuffle(coaEntitiesCopy);
        List<CoaEntity> selectedCoaEntities = coaEntitiesCopy.subList(0, 5)
                .stream()
                .sorted(Comparator.comparing(CoaEntity::getCoaId))
                .toList();
        List<Integer> selectedCoaIds = selectedCoaEntities.stream()
                .map(CoaEntity::getCoaId)
                .toList();

        List<CoaItemDto> coaItemsList = given().contentType(ContentType.JSON)
                .body(selectedCoaIds)
                .when()
                .post("/get")
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });
        for (int i = 0; i < selectedCoaEntities.size(); i++) {
            CoaEntity coaEntity = selectedCoaEntities.get(i);
            CoaItemDto response = coaItemsList.get(i);
            Assertions.assertEquals(coaEntity.getCoaId(), response.coaId());
            Assertions.assertEquals(coaEntity.getCoaText(), response.coaText());
            Assertions.assertEquals(coaEntity.getCoaDescription(), response.coaDescription());
            Assertions.assertEquals(coaEntity.getClientName(), response.clientName());
            Assertions.assertEquals(coaEntity.getLvl1Category(), response.lvl1Category());
            Assertions.assertEquals(coaEntity.getIsActive(), response.isActive());
            Assertions.assertEquals(coaEntity.getSign(), response.sign());
        }
    }
}
