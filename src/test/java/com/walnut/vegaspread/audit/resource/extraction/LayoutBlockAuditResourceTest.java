package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.entity.extraction.LayoutBlockAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.LayoutBlockAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.coa.CoaItemAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(LayoutBlockAuditResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class LayoutBlockAuditResourceTest {

    @Inject
    LayoutBlockAuditRepository layoutBlockAuditRepository;

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<LayoutBlockAuditEntity> createLayoutBlockAuditItems() {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = new ArrayList<>();

        LayoutBlockAuditEntity layoutBlockAuditEntity1 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(1)
                        .setColName("tag")
                        .setPrevValue(null)
                        .setNewValue("tag1")
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now());
        layoutBlockAuditEntities.add(layoutBlockAuditEntity1);

        LayoutBlockAuditEntity layoutBlockAuditEntity2 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(4)
                        .setColName("tag")
                        .setPrevValue(null)
                        .setNewValue("tag2")
                        .setAction(AuditStatus.CREATED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now());
        layoutBlockAuditEntities.add(layoutBlockAuditEntity2);

        LayoutBlockAuditEntity layoutBlockAuditEntity3 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(4)
                        .setColName("tag")
                        .setPrevValue("tag2")
                        .setNewValue("Balance Sheet")
                        .setAction(AuditStatus.UPDATED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now());
        layoutBlockAuditEntities.add(layoutBlockAuditEntity3);

        LayoutBlockAuditEntity layoutBlockAuditEntity4 =
                (LayoutBlockAuditEntity) new LayoutBlockAuditEntity()
                        .setBlockId(5)
                        .setColName("block_id")
                        .setPrevValue("1234")
                        .setNewValue(null)
                        .setAction(AuditStatus.DELETED)
                        .setAuditedBy(USERNAME)
                        .setAuditedBYFullName(NAME)
                        .setAuditTime(LocalDateTime.now());
        layoutBlockAuditEntities.add(layoutBlockAuditEntity4);

        layoutBlockAuditRepository.persist(layoutBlockAuditEntities);
        return layoutBlockAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreateForEmptyList() {

        List<CoaItemAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditedCreateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        List<LayoutBlockAuditDto.Create> auditItems = List.of(
                new LayoutBlockAuditDto.Create(1, "x_min", String.valueOf(111)),
                new LayoutBlockAuditDto.Create(1, "y_min", String.valueOf(222))
        );

        List<LayoutBlockAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditedCreateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            LayoutBlockAuditEntity layoutBlockAuditEntity = layoutBlockAuditRepository.findById(
                    auditedCreateItems.get(i).id());
            LayoutBlockAuditDto.Create auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.blockId(), layoutBlockAuditEntity.getBlockId());
            Assertions.assertEquals(auditItem.colName(), layoutBlockAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), layoutBlockAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.CREATED, layoutBlockAuditEntity.getAction());
            Assertions.assertNull(layoutBlockAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdateForEmptyList() {

        List<LayoutBlockAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {

        List<LayoutBlockAuditDto.Update> auditItems = List.of(
                new LayoutBlockAuditDto.Update(1, "tag", null,
                        "newTag"),
                new LayoutBlockAuditDto.Update(2, "comment", "oldComment",
                        "newComment"));
        List<LayoutBlockAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            LayoutBlockAuditEntity layoutBlockAuditEntity = layoutBlockAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            LayoutBlockAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.blockId(), layoutBlockAuditEntity.getBlockId());
            Assertions.assertEquals(auditItem.colName(), layoutBlockAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), layoutBlockAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, layoutBlockAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), layoutBlockAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDeleteForEmptyList() {

        List<LayoutBlockAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditDeleteItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {

        List<LayoutBlockAuditDto.Delete> auditItems = List.of(
                new LayoutBlockAuditDto.Delete(1, "tag", "tag1"),
                new LayoutBlockAuditDto.Delete(2, "comment", "comment1"));
        List<LayoutBlockAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditDeleteItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            LayoutBlockAuditEntity layoutBlockAuditEntity = layoutBlockAuditRepository.findById(
                    auditDeleteItems.get(i).id());
            LayoutBlockAuditDto.Delete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.blockId(), layoutBlockAuditEntity.getBlockId());
            Assertions.assertEquals(auditItem.colName(), layoutBlockAuditEntity.getColName());
            Assertions.assertNull(layoutBlockAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.DELETED, layoutBlockAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), layoutBlockAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testListAuditsForBlocksAndColWithEmptyBlockList() {
        createLayoutBlockAuditItems();
        String colName = "tag";

        List<LayoutBlockAuditDto.Response> responseList = given().when()
                .contentType(ContentType.JSON)
                .pathParam("colName", colName)
                .body(Collections.emptyList())
                .post("/{colName}/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(responseList.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testListAuditsForBlocksAndCol() {
        List<LayoutBlockAuditEntity> layoutBlockAuditEntities = createLayoutBlockAuditItems();
        List<Integer> blockIds = List.of(1, 4);
        String colName = "tag";

        List<LayoutBlockAuditDto.Response> responseList = given().when()
                .contentType(ContentType.JSON)
                .pathParam("colName", colName)
                .body(blockIds)
                .post("/{colName}/list")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<LayoutBlockAuditEntity> auditsForTag = layoutBlockAuditEntities.stream()
                .filter(layoutBlockAuditEntity ->
                        colName.equals(layoutBlockAuditEntity.getColName())
                                && blockIds.contains(layoutBlockAuditEntity.getBlockId()))
                .toList();
        Assertions.assertEquals(auditsForTag.size(), responseList.size());
        List<LayoutBlockAuditDto.Response> auditDtosForTag = LayoutBlockAuditEntity.toDtoList(auditsForTag);

        for (int i = 0; i < auditsForTag.size(); i++) {
            LayoutBlockAuditDto.Response auditDtoForTag = auditDtosForTag.get(i);
            LayoutBlockAuditDto.Response auditItem = responseList.get(i);
            Assertions.assertEquals(auditDtoForTag.blockId(), auditItem.blockId());
            Assertions.assertEquals(auditDtoForTag.colName(), colName);
            Assertions.assertEquals(auditDtoForTag.newValue(), auditItem.newValue());
            Assertions.assertEquals(auditDtoForTag.action(), auditItem.action());
            Assertions.assertEquals(auditDtoForTag.prevValue(), auditItem.prevValue());
        }
    }
}
