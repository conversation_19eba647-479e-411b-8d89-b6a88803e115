package com.walnut.vegaspread.audit.resource.workflow;

import com.walnut.vegaspread.audit.entity.workflow.ReviewAuditEntity;
import com.walnut.vegaspread.audit.repository.workflow.ReviewAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.model.audit.workflow.ReviewAuditDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(ReviewAuditResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ReviewAuditResourceTest {

    @Inject
    ReviewAuditRepository reviewAuditRepository;

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<ReviewAuditEntity> createReviewAudits() {
        List<ReviewAuditEntity> reviewAuditEntities = new ArrayList<>();

        ReviewAuditEntity reviewAuditEntity1 = (ReviewAuditEntity) new ReviewAuditEntity()
                .setReviewId(1)
                .setColName("status")
                .setPrevValue(null)
                .setNewValue("PENDING")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        reviewAuditEntities.add(reviewAuditEntity1);

        ReviewAuditEntity reviewAuditEntity2 = (ReviewAuditEntity) new ReviewAuditEntity()
                .setReviewId(1)
                .setColName("status")
                .setPrevValue("PENDING")
                .setNewValue("SENT_FOR_APPROVAL")
                .setAction(AuditStatus.UPDATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        reviewAuditEntities.add(reviewAuditEntity2);

        ReviewAuditEntity reviewAuditEntity3 = (ReviewAuditEntity) new ReviewAuditEntity()
                .setReviewId(1)
                .setColName("status")
                .setPrevValue("SENT_FOR_APPROVAL")
                .setNewValue("APPROVED")
                .setAction(AuditStatus.UPDATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        reviewAuditEntities.add(reviewAuditEntity3);

        ReviewAuditEntity reviewAuditEntity4 = (ReviewAuditEntity) new ReviewAuditEntity()
                .setReviewId(2)
                .setColName("status")
                .setPrevValue(null)
                .setNewValue("PENDING")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        reviewAuditEntities.add(reviewAuditEntity4);

        ReviewAuditEntity reviewAuditEntity5 = (ReviewAuditEntity) new ReviewAuditEntity()
                .setReviewId(2)
                .setColName("status")
                .setPrevValue("PENDING")
                .setNewValue("SENT_FOR_APPROVAL")
                .setAction(AuditStatus.UPDATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        reviewAuditEntities.add(reviewAuditEntity5);

        ReviewAuditEntity reviewAuditEntity6 = (ReviewAuditEntity) new ReviewAuditEntity()
                .setReviewId(2)
                .setColName("status")
                .setPrevValue(null)
                .setNewValue("PENDING")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        reviewAuditEntities.add(reviewAuditEntity6);

        reviewAuditRepository.persist(reviewAuditEntities);
        return reviewAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreateForEmptyList() {

        List<ReviewAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditedCreateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        List<ReviewAuditDto.Create> auditItems = List.of(
                new ReviewAuditDto.Create(1, "status", "PENDING"),
                new ReviewAuditDto.Create(2, "status", "PENDING")
        );

        List<ReviewAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditedCreateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ReviewAuditEntity reviewAuditEntity = reviewAuditRepository.findById(
                    auditedCreateItems.get(i).id());
            ReviewAuditDto.Create auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.reviewId(), reviewAuditEntity.getReviewId());
            Assertions.assertEquals(auditItem.colName(), reviewAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), reviewAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.CREATED, reviewAuditEntity.getAction());
            Assertions.assertNull(reviewAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdateForEmptyList() {

        List<ReviewAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {

        List<ReviewAuditDto.Update> auditItems = List.of(
                new ReviewAuditDto.Update(1, "status", "PENDING",
                        "SENT_FOR_APPROVAL"),
                new ReviewAuditDto.Update(2, "status", "SENT_FOR_APPROVAL",
                        "PENDING"));

        List<ReviewAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ReviewAuditEntity reviewAuditEntity = reviewAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            ReviewAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.reviewId(), reviewAuditEntity.getReviewId());
            Assertions.assertEquals(auditItem.colName(), reviewAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), reviewAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, reviewAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), reviewAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testGet() {
        List<ReviewAuditEntity> reviewAuditEntities = createReviewAudits();

        ReviewAuditEntity reviewAuditEntity = reviewAuditEntities.get(1);

        ReviewAuditDto.Response reviewAuditResponse = given().when()
                .contentType(ContentType.JSON)
                .pathParam("auditId", 2)
                .get("/audit/{auditId}")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(reviewAuditEntity.getId(), reviewAuditResponse.id());
        Assertions.assertEquals(reviewAuditEntity.getReviewId(), reviewAuditResponse.reviewId());
        Assertions.assertEquals(reviewAuditEntity.getColName(), reviewAuditResponse.colName());
        Assertions.assertEquals(reviewAuditEntity.getPrevValue(), reviewAuditResponse.prevValue());
        Assertions.assertEquals(reviewAuditEntity.getNewValue(), reviewAuditResponse.newValue());
        Assertions.assertEquals(reviewAuditEntity.getAuditedBy(), reviewAuditResponse.auditedBy());
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetByReviewId() {
        List<ReviewAuditEntity> reviewAuditEntities = createReviewAudits();

        ReviewAuditEntity reviewAuditEntity = reviewAuditEntities.get(3);
        List<ReviewAuditDto.Response> reviewAudits = given().when()
                .contentType(ContentType.JSON)
                .pathParam("reviewId", reviewAuditEntity.getReviewId())
                .get("/{reviewId}")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<ReviewAuditEntity> filteredEntities = reviewAuditEntities.stream()
                .filter(reviewAudit -> reviewAuditEntity.getReviewId().equals(reviewAudit.getReviewId()))
                .toList();

        Assertions.assertEquals(filteredEntities.size(), reviewAudits.size());

        for (int i = 0; i < filteredEntities.size(); i++) {
            ReviewAuditDto.Response reviewAuditResponse = reviewAudits.get(i);
            ReviewAuditEntity filteredEntity = filteredEntities.get(i);
            Assertions.assertEquals(filteredEntity.getId(), reviewAuditResponse.id());
            Assertions.assertEquals(filteredEntity.getReviewId(), reviewAuditResponse.reviewId());
            Assertions.assertEquals(filteredEntity.getColName(), reviewAuditResponse.colName());
            Assertions.assertEquals(filteredEntity.getPrevValue() == null ? "NA" : filteredEntity.getPrevValue(),
                    reviewAuditResponse.prevValue());
            Assertions.assertEquals(filteredEntity.getNewValue() == null ? "NA" : filteredEntity.getNewValue(),
                    reviewAuditResponse.newValue());
            Assertions.assertEquals(filteredEntity.getAuditedBy(), reviewAuditResponse.auditedBy());
        }
    }
}
