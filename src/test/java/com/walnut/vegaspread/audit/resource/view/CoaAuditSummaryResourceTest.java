package com.walnut.vegaspread.audit.resource.view;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedRowCoaDataAuditEntity;
import com.walnut.vegaspread.audit.entity.view.AuditMetricsEntity;
import com.walnut.vegaspread.audit.model.view.CoaAuditSummary;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedRowCoaDataAuditRepository;
import com.walnut.vegaspread.audit.repository.view.AuditMetricsRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.audit.service.view.CoaAuditSummaryService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.junit.mockito.InjectSpy;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.model.audit.shared.AuditStatus.UPDATED;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestHTTPEndpoint(CoaAuditSummaryResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class CoaAuditSummaryResourceTest {

    private static final Logger log = LoggerFactory.getLogger(CoaAuditSummaryResourceTest.class);
    @Inject
    ExtractedRowCoaDataAuditRepository extractedRowCoaDataAuditRepository;

    @InjectSpy
    CoaAuditSummaryService coaAuditSummaryService;

    @Inject
    AuditMetricsRepository auditMetricsRepository;

    @Inject
    Flyway flyway;

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    EntityManager entityManager;

    @Inject
    DataSource dataSource;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @BeforeEach
    void initData() {
        setupData();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    void setupData() {
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 0)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("2")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now())
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 8)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("47")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(1))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 2)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("603")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(2))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 5)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("55")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(3))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 12)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("48")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(4))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 16)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("163")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(5))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 24)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("75")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(6))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 0)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(7))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 2)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("216")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(8))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 7)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("242")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(9))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 10)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(10))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 11)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(11))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 12)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("242")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(12))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 13)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("216")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(13))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 18)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("242")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(14))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 19)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(15))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 20)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(16))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 23)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(17))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 24)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("242")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(18))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 26)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(19))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 27)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(20))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 28)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(21))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 29)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(22))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 30)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(23))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 34)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("264")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(24))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("184")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(25))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 15)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(26))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 16)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("262")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(27))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 22)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(28))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 5)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("193")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(29))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 21)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("212")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(29))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 25)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("250")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(30))
                        .setAuditedBy("developer"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 0)
                        .setColName("header_ids")
                        .setPrevValue("[]")
                        .setNewValue("[1, 2]")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(31))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("7")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(31))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 2)
                        .setColName("coa_id")
                        .setPrevValue("603")
                        .setNewValue("25")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(32))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 3)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("34")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(33))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 5)
                        .setColName("coa_id")
                        .setPrevValue("55")
                        .setNewValue("44")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(34))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 6)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("46")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(35))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 7)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("51")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(36))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 8)
                        .setColName("coa_id")
                        .setPrevValue("47")
                        .setNewValue("51")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(37))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 9)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("50")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(38))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 10)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("47")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(39))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 12)
                        .setColName("coa_id")
                        .setPrevValue("48")
                        .setNewValue("61")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(40))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 15)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("99")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(41))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 15)
                        .setColName("coa_id")
                        .setPrevValue("99")
                        .setNewValue("126")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(42))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 16)
                        .setColName("coa_id")
                        .setPrevValue("163")
                        .setNewValue("120")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(43))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 17)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("120")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(44))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 18)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("126")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(45))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 19)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("107")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(46))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 20)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("94")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(47))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453300)
                        .setRowId((short) 24)
                        .setColName("coa_id")
                        .setPrevValue("75")
                        .setNewValue("172")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(48))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 0)
                        .setColName("header_ids")
                        .setPrevValue("[]")
                        .setNewValue("[1, 2]")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(49))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 0)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("561")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(50))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 1)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("561")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(51))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 2)
                        .setColName("coa_id")
                        .setPrevValue("216")
                        .setNewValue("250")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(52))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 4)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("193")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(53))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 7)
                        .setColName("coa_id")
                        .setPrevValue("242")
                        .setNewValue("193")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(54))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 10)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("216")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(55))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 11)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(56))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 12)
                        .setColName("coa_id")
                        .setPrevValue("242")
                        .setNewValue("219")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(57))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 13)
                        .setColName("coa_id")
                        .setPrevValue("216")
                        .setNewValue("227")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(58))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 14)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(59))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 16)
                        .setColName("coa_id")
                        .setPrevValue("262")
                        .setNewValue("217")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(60))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 17)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(61))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 18)
                        .setColName("coa_id")
                        .setPrevValue("242")
                        .setNewValue("239")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(62))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 19)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("223")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(63))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 20)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(
                                LocalDateTime.now().plusSeconds(64))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 21)
                        .setColName("coa_id")
                        .setPrevValue("212")
                        .setNewValue("216")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(65))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 23)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("230")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(66))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 24)
                        .setColName("coa_id")
                        .setPrevValue("242")
                        .setNewValue("217")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(67))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 25)
                        .setColName("coa_id")
                        .setPrevValue("250")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(68))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 26)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(69))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 27)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(70))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 28)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("214")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(71))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 29)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("207")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(72))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 30)
                        .setColName("coa_id")
                        .setPrevValue("184")
                        .setNewValue("228")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(73))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 33)
                        .setColName("coa_id")
                        .setPrevValue("1")
                        .setNewValue("271")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(74))
                        .setAuditedBy("ahona"));
        extractedRowCoaDataAuditRepository.persist(
                (ExtractedRowCoaDataAuditEntity) new ExtractedRowCoaDataAuditEntity().setTableId(453299)
                        .setRowId((short) 34)
                        .setColName("coa_id")
                        .setPrevValue("264")
                        .setNewValue("1")
                        .setAction(UPDATED)
                        .setAuditTime(LocalDateTime.now().plusSeconds(75))
                        .setAuditedBy("ahona"));
    }

    Map<Integer, String> getCoaIdCategoryMap() {
        Map<Integer, String> coaIdCategoryMap = new HashMap<>();
        coaIdCategoryMap.put(1, "");
        coaIdCategoryMap.put(2, "Current Assets");
        coaIdCategoryMap.put(7, "Current Assets");
        coaIdCategoryMap.put(25, "Current Assets");
        coaIdCategoryMap.put(34, "Current Assets");
        coaIdCategoryMap.put(44, "Non Current Assets");
        coaIdCategoryMap.put(46, "Non Current Assets");
        coaIdCategoryMap.put(47, "Non Current Assets");
        coaIdCategoryMap.put(48, "Non Current Assets");
        coaIdCategoryMap.put(50, "Non Current Assets");
        coaIdCategoryMap.put(51, "Non Current Assets");
        coaIdCategoryMap.put(55, "Non Current Assets");
        coaIdCategoryMap.put(61, "Non Current Assets");
        coaIdCategoryMap.put(75, "Non Current Assets");
        coaIdCategoryMap.put(94, "Current Liabilities");
        coaIdCategoryMap.put(99, "Current Liabilities");
        coaIdCategoryMap.put(107, "Current Liabilities");
        coaIdCategoryMap.put(120, "Current Liabilities");
        coaIdCategoryMap.put(126, "Current Liabilities");
        coaIdCategoryMap.put(163, "Non Current Liabilities");
        coaIdCategoryMap.put(172, "Net Worth");
        coaIdCategoryMap.put(184, "Income Statement");
        coaIdCategoryMap.put(193, "Income Statement");
        coaIdCategoryMap.put(207, "Income Statement");
        coaIdCategoryMap.put(212, "Income Statement");
        coaIdCategoryMap.put(214, "Income Statement");
        coaIdCategoryMap.put(216, "Income Statement");
        coaIdCategoryMap.put(217, "Income Statement");
        coaIdCategoryMap.put(219, "Income Statement");
        coaIdCategoryMap.put(223, "Income Statement");
        coaIdCategoryMap.put(227, "Income Statement");
        coaIdCategoryMap.put(228, "Income Statement");
        coaIdCategoryMap.put(230, "Income Statement");
        coaIdCategoryMap.put(239, "Income Statement");
        coaIdCategoryMap.put(242, "Income Statement");
        coaIdCategoryMap.put(250, "Income Statement");
        coaIdCategoryMap.put(262, "Income Statement");
        coaIdCategoryMap.put(264, "Income Statement");
        coaIdCategoryMap.put(271, "Income Statement");
        coaIdCategoryMap.put(561, "Income Statement");
        coaIdCategoryMap.put(603, "Current Assets");
        return coaIdCategoryMap;
    }

    @Test
    @TestSecurity(user = NAME)
    void testCalculateAccuracyForNewCompletedDocument() throws Exception {

        UUID docId = UUID.fromString("c7998766-5ce6-4010-9e90-f5d0fb939fd3");
        when(exchangeService.getBlockIdsForDoc(docId)).thenReturn(List.of(453299, 453300));
        when(exchangeService.getCategoryForCoaIds(
                List.of(1, 271, 264, 126, 212, 216, 107, 184, 207, 230, 163, 120, 262, 217, 94, 223, 242, 239, 214, 75,
                        172, 228, 250, 193, 603, 25, 34, 2, 7, 46, 561, 51, 55, 44, 47, 227, 219, 50, 99, 48,
                        61))).thenReturn(getCoaIdCategoryMap());

        printAllTablesAndData();
        CoaAuditSummary.CoaAuditSummaryOutput summary = given()
                .contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .post("/accuracy/{docId}")
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertEquals(
                List.of(
                        new CoaAuditSummary.CoaAuditSummaryHeader(0, "COA"),
                        new CoaAuditSummary.CoaAuditSummaryHeader(1, "Completeness"),
                        new CoaAuditSummary.CoaAuditSummaryHeader(2, "Accuracy")
                )
                , summary.headers());
        Map<String, List<String>> coaCellsTextMap =
                Map.of(
                        "Current Assets",
                        List.of("Current Assets", "50", "25"),
                        "Non Current Assets",
                        List.of("Non Current Assets", "50", "0"),
                        "Current Liabilities",
                        List.of("Current Liabilities", "25", "0"),
                        "Non Current Liabilities",
                        List.of("Non Current Liabilities", "0", "0"),
                        "Net Worth",
                        List.of("Net Worth", "100", "0"),
                        "Income Statement",
                        List.of("Income Statement", "93", "11")
                );
        for (int i = 0; i < 6; i++) {
            CoaAuditSummary.CoaAuditSummaryRow currentRow = summary.rows().get(i);
            String coa = currentRow.cellsText().get(0);
            Assertions.assertEquals(new CoaAuditSummary.CoaAuditSummaryRow(i, coaCellsTextMap.get(coa)),
                    currentRow);
        }
        verify(exchangeService, times(0)).getNamesFromUsernames(anyList());
    }

    public void printAllTablesAndData() throws Exception {
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement();
             ResultSet tables = stmt.executeQuery("SHOW TABLES")) { // MySQL-style query

            while (tables.next()) {
                String tableName = tables.getString(1);
                if (tableName.equalsIgnoreCase("FLYWAY_SCHEMA_HISTORY")) {
                    continue;
                }
                System.out.println("\n===== TABLE: " + tableName + " =====");

                try (Statement dataStmt = conn.createStatement();
                     ResultSet data = dataStmt.executeQuery("SELECT * FROM " + tableName)) {

                    int columnCount = data.getMetaData().getColumnCount();

                    // Print column names
                    for (int i = 1; i <= columnCount; i++) {
                        System.out.print(data.getMetaData().getColumnName(i) + "\t");
                    }
                    System.out.println("\n----------------------------------");

                    // Print row data
                    while (data.next()) {
                        for (int i = 1; i <= columnCount; i++) {
                            System.out.print(data.getString(i) + "\t");
                        }
                        System.out.println();
                    }
                }
            }
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetAccuracy() {

        UUID docId = UUID.fromString("c7998766-5ce6-4010-9e90-f5d0fb939fd3");
        setupAuditMetrics(docId);

        CoaAuditSummary.CoaAuditSummaryOutput summary = given()
                .contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .get("/accuracy/{docId}")
                .then().statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertEquals(
                List.of(
                        new CoaAuditSummary.CoaAuditSummaryHeader(0, "COA"),
                        new CoaAuditSummary.CoaAuditSummaryHeader(1, "Completeness"),
                        new CoaAuditSummary.CoaAuditSummaryHeader(2, "Accuracy")
                )
                , summary.headers());
        Map<String, List<String>> coaCellsTextMap =
                Map.of(
                        "Current Assets",
                        List.of("Current Assets", "100", "0"),
                        "Non Current Assets",
                        List.of("Non Current Assets", "90", "10"),
                        "Current Liabilities",
                        List.of("Current Liabilities", "80", "20"),
                        "Non Current Liabilities",
                        List.of("Non Current Liabilities", "70", "30"),
                        "Net Worth",
                        List.of("Net Worth", "60", "40"),
                        "Income Statement",
                        List.of("Income Statement", "50", "50")
                );
        for (int i = 0; i < 6; i++) {

            CoaAuditSummary.CoaAuditSummaryRow currentRow = summary.rows().get(i);
            String coa = currentRow.cellsText().get(0);
            Assertions.assertEquals(new CoaAuditSummary.CoaAuditSummaryRow(i, coaCellsTextMap.get(coa)),
                    currentRow);
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetAccuracyForMissingCategory() {

        UUID docId = UUID.fromString("c7998766-5ce6-4010-9e90-f5d0fb939fd3");
        List<AuditMetricsEntity> auditMetricsEntities = setupAuditMetrics(docId);
        removeAuditMetricsForId(auditMetricsEntities.get(0).getId());

        try {
            given()
                    .contentType(ContentType.JSON)
                    .pathParam("docId", docId)
                    .get("/accuracy/{docId}")
                    .then().statusCode(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode());
        } catch (IllegalStateException e) {
            Assertions.assertEquals("Missing audit metric for category " + auditMetricsEntities.get(0)
                    .getCategory() + " for document " + docId, e.getMessage());
        }
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetAccuracyForMissingAccuracyDataForDoc() {

        UUID docId = UUID.fromString("c7998766-5ce6-4010-9e90-f5d0fb939fd3");

        given()
                .contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .get("/accuracy/{docId}")
                .then().statusCode(Response.Status.OK.getStatusCode());

        verify(coaAuditSummaryService, times(1)).calculateAccuracy(docId);
    }

    @Transactional
    void removeAuditMetricsForId(Long id) {
        auditMetricsRepository.deleteById(id);
    }

    @Transactional
    List<AuditMetricsEntity> setupAuditMetrics(UUID docId) {
        List<AuditMetricsEntity> auditMetricsEntities = new ArrayList<>();
        AuditMetricsEntity auditMetricsEntity1 = new AuditMetricsEntity().setDocId(docId)
                .setCategory("Current Assets")
                .setCompleteness(100)
                .setAccuracy(0)
                .setCreatedBy(USERNAME)
                .setCreatedTime(LocalDateTime.now());
        auditMetricsEntities.add(auditMetricsEntity1);

        AuditMetricsEntity auditMetricsEntity2 = new AuditMetricsEntity().setDocId(docId)
                .setCategory("Non Current Assets")
                .setCompleteness(90)
                .setAccuracy(10)
                .setCreatedBy(USERNAME)
                .setCreatedTime(LocalDateTime.now());
        auditMetricsEntities.add(auditMetricsEntity2);

        AuditMetricsEntity auditMetricsEntity3 = new AuditMetricsEntity().setDocId(docId)
                .setCategory("Current Liabilities")
                .setCompleteness(80)
                .setAccuracy(20)
                .setCreatedBy(USERNAME)
                .setCreatedTime(LocalDateTime.now());
        auditMetricsEntities.add(auditMetricsEntity3);

        AuditMetricsEntity auditMetricsEntity4 = new AuditMetricsEntity().setDocId(docId)
                .setCategory("Non Current Liabilities")
                .setCompleteness(70)
                .setAccuracy(30)
                .setCreatedBy(USERNAME)
                .setCreatedTime(LocalDateTime.now());
        auditMetricsEntities.add(auditMetricsEntity4);

        AuditMetricsEntity auditMetricsEntity5 = new AuditMetricsEntity().setDocId(docId)
                .setCategory("Net Worth")
                .setCompleteness(60)
                .setAccuracy(40)
                .setCreatedBy(USERNAME)
                .setCreatedTime(LocalDateTime.now());
        auditMetricsEntities.add(auditMetricsEntity5);

        AuditMetricsEntity auditMetricsEntity6 = new AuditMetricsEntity().setDocId(docId)
                .setCategory("Income Statement")
                .setCompleteness(50)
                .setAccuracy(50)
                .setCreatedBy(USERNAME)
                .setCreatedTime(LocalDateTime.now());
        auditMetricsEntities.add(auditMetricsEntity6);

        auditMetricsRepository.persist(auditMetricsEntities);
        return auditMetricsEntities;
    }
}
