package com.walnut.vegaspread.audit.resource.workflow;

import com.walnut.vegaspread.audit.entity.workflow.DocumentAuditEntity;
import com.walnut.vegaspread.audit.repository.workflow.DocumentAuditRepository;
import com.walnut.vegaspread.audit.service.common.ExchangeService;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import com.walnut.vegaspread.common.model.audit.workflow.DocumentAuditDto;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

import static com.walnut.vegaspread.audit.resource.CommonTestUtils.NAME;
import static com.walnut.vegaspread.audit.resource.CommonTestUtils.USERNAME;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@QuarkusTest
@TestHTTPEndpoint(value = DocumentAuditResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class DocumentAuditResourceTest {

    @Inject
    DocumentAuditRepository documentAuditRepository;

    @InjectMock
    ExchangeService exchangeService;

    @Inject
    Flyway flyway;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<DocumentAuditEntity> createDocumentAudits() {
        List<DocumentAuditEntity> documentAuditEntities = new ArrayList<>();

        UUID docId1 = UUID.randomUUID();
        DocumentAuditEntity documentAuditEntity1 = (DocumentAuditEntity) new DocumentAuditEntity()
                .setDocId(docId1)
                .setColName("last_modified_by")
                .setPrevValue(null)
                .setNewValue("developer")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        documentAuditEntities.add(documentAuditEntity1);

        DocumentAuditEntity documentAuditEntity2 = (DocumentAuditEntity) new DocumentAuditEntity()
                .setDocId(docId1)
                .setColName("last_modified_by")
                .setPrevValue("developer")
                .setNewValue("user1")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        documentAuditEntities.add(documentAuditEntity2);

        DocumentAuditEntity documentAuditEntity3 = (DocumentAuditEntity) new DocumentAuditEntity()
                .setDocId(UUID.randomUUID())
                .setColName("period")
                .setPrevValue("2022-12-31")
                .setNewValue("2023-12-31")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        documentAuditEntities.add(documentAuditEntity3);

        UUID docId2 = UUID.randomUUID();
        DocumentAuditEntity documentAuditEntity4 = (DocumentAuditEntity) new DocumentAuditEntity()
                .setDocId(docId2)
                .setColName("spread_level")
                .setPrevValue(null)
                .setNewValue("STANDALONE")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        documentAuditEntities.add(documentAuditEntity4);

        DocumentAuditEntity documentAuditEntity5 = (DocumentAuditEntity) new DocumentAuditEntity()
                .setDocId(docId2)
                .setColName("spread_level")
                .setPrevValue("STANDALONE")
                .setNewValue("CONSOLIDATED")
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        documentAuditEntities.add(documentAuditEntity5);

        DocumentAuditEntity documentAuditEntity6 = (DocumentAuditEntity) new DocumentAuditEntity()
                .setDocId(docId2)
                .setColName("spread_level")
                .setPrevValue("CONSOLIDATED")
                .setNewValue(null)
                .setAction(AuditStatus.CREATED)
                .setAuditedBy(USERNAME)
                .setAuditedBYFullName(NAME)
                .setAuditTime(LocalDateTime.now());
        documentAuditEntities.add(documentAuditEntity6);

        documentAuditRepository.persist(documentAuditEntities);
        return documentAuditEntities;
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreateForEmptyList() {

        List<DocumentAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditedCreateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForCreate() {
        List<DocumentAuditDto.Create> auditItems = List.of(
                new DocumentAuditDto.Create(UUID.randomUUID(), "spread_level", "CONSOLIDATED"),
                new DocumentAuditDto.Create(UUID.randomUUID(), "status", "UNDER_REVIEW_LVL_1")
        );

        List<DocumentAuditDto.Response> auditedCreateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-create")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditedCreateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            DocumentAuditEntity documentAuditEntity = documentAuditRepository.findById(
                    auditedCreateItems.get(i).id());
            DocumentAuditDto.Create auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.docId(), documentAuditEntity.getDocId());
            Assertions.assertEquals(auditItem.colName(), documentAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), documentAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.CREATED, documentAuditEntity.getAction());
            Assertions.assertNull(documentAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdateForEmptyList() {

        List<DocumentAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForUpdate() {
        UUID docId = UUID.randomUUID();

        List<DocumentAuditDto.UpdateOrDelete> auditItems = List.of(
                new DocumentAuditDto.UpdateOrDelete(docId, "spread_level", "CONSOLIDATED",
                        "STANDALONE"),
                new DocumentAuditDto.UpdateOrDelete(docId, "status", "UNDER_REVIEW_LVL_1",
                        "UNDER_REVIEW_LVL_2"));

        List<DocumentAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            DocumentAuditEntity documentAuditEntity = documentAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            DocumentAuditDto.UpdateOrDelete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.docId(), documentAuditEntity.getDocId());
            Assertions.assertEquals(auditItem.colName(), documentAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), documentAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, documentAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), documentAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDeleteForEmptyList() {

        List<DocumentAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditDeleteItems.isEmpty());
    }

    @Test
    @TestSecurity(user = NAME)
    void testAuditForDelete() {

        UUID docId = UUID.randomUUID();

        List<DocumentAuditDto.UpdateOrDelete> auditItems = List.of(
                new DocumentAuditDto.UpdateOrDelete(docId, "spread_level", "CONSOLIDATED",
                        null),
                new DocumentAuditDto.UpdateOrDelete(docId, "status", "UNDER_REVIEW_LVL_1",
                        "UNDER_REVIEW_WALNUT"));

        List<DocumentAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post("/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditDeleteItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            DocumentAuditEntity documentAuditEntity = documentAuditRepository.findById(
                    auditDeleteItems.get(i).id());
            DocumentAuditDto.UpdateOrDelete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.docId(), documentAuditEntity.getDocId());
            Assertions.assertEquals(auditItem.colName(), documentAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), documentAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.DELETED, documentAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), documentAuditEntity.getPrevValue());
        }
        verify(exchangeService, times(1)).getNameFromUsername(NAME);
    }

    @Test
    @TestSecurity(user = NAME)
    void testGet() {
        List<DocumentAuditEntity> documentAuditEntities = createDocumentAudits();

        DocumentAuditEntity documentAuditEntity = documentAuditEntities.get(0);
        DocumentAuditDto.Response documentAuditResponse = given().when()
                .contentType(ContentType.JSON)
                .pathParam("auditId", documentAuditEntity.getId())
                .get("/audit/{auditId}")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(documentAuditEntity.getId(), documentAuditResponse.id());
        Assertions.assertEquals(documentAuditEntity.getDocId(), documentAuditResponse.docId());
        Assertions.assertEquals(documentAuditEntity.getColName(), documentAuditResponse.colName());
        Assertions.assertEquals(documentAuditEntity.getPrevValue() == null ? "NA" : documentAuditEntity.getPrevValue(),
                documentAuditResponse.prevValue());
        Assertions.assertEquals(documentAuditEntity.getNewValue() == null ? "NA" : documentAuditEntity.getNewValue(),
                documentAuditResponse.newValue());
        Assertions.assertEquals(documentAuditEntity.getAuditedBy(), documentAuditResponse.auditedBy());
    }

    @Test
    @TestSecurity(user = NAME)
    void testGetByDocId() {
        List<DocumentAuditEntity> documentAuditEntities = createDocumentAudits();

        DocumentAuditEntity documentAuditEntity = documentAuditEntities.get(4);
        List<DocumentAuditDto.Response> documentAudits = given().when()
                .contentType(ContentType.JSON)
                .pathParam("docId", documentAuditEntity.getDocId())
                .get("/{docId}")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        List<DocumentAuditEntity> filteredEntities = documentAuditEntities.stream()
                .filter(docEntity -> documentAuditEntity.getDocId().equals(docEntity.getDocId()))
                .toList();

        Assertions.assertEquals(filteredEntities.size(), documentAudits.size());

        for (int i = 0; i < filteredEntities.size(); i++) {
            DocumentAuditDto.Response documentAuditResponse = documentAudits.get(i);
            DocumentAuditEntity filteredEntity = filteredEntities.get(i);
            Assertions.assertEquals(filteredEntity.getId(), documentAuditResponse.id());
            Assertions.assertEquals(filteredEntity.getDocId(), documentAuditResponse.docId());
            Assertions.assertEquals(filteredEntity.getColName(), documentAuditResponse.colName());
            Assertions.assertEquals(filteredEntity.getPrevValue() == null ? "NA" : filteredEntity.getPrevValue(),
                    documentAuditResponse.prevValue());
            Assertions.assertEquals(filteredEntity.getNewValue() == null ? "NA" : filteredEntity.getNewValue(),
                    documentAuditResponse.newValue());
            Assertions.assertEquals(filteredEntity.getAuditedBy(), documentAuditResponse.auditedBy());
        }
    }
}
