package com.walnut.vegaspread.audit.resource.extraction;

import com.walnut.vegaspread.audit.entity.extraction.ExtractedTableRowAuditEntity;
import com.walnut.vegaspread.audit.entity.extraction.LayoutBlockAuditEntity;
import com.walnut.vegaspread.audit.repository.extraction.ExtractedTableRowAuditRepository;
import com.walnut.vegaspread.audit.repository.extraction.LayoutBlockAuditRepository;
import com.walnut.vegaspread.common.model.audit.extraction.ExtractedTableRowAuditDto;
import com.walnut.vegaspread.common.model.audit.extraction.LayoutBlockAuditDto;
import com.walnut.vegaspread.common.model.audit.shared.AuditStatus;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.Collections;
import java.util.List;

import static com.walnut.vegaspread.audit.resource.extraction.ApiAuthResource.BLOCK_PREFIX;
import static com.walnut.vegaspread.audit.resource.extraction.ApiAuthResource.ROW_PREFIX;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ApiAuthResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ApiAuthResourceTest {

    @Inject
    Flyway flyway;

    @Inject
    ExtractedTableRowAuditRepository extractedTableRowAuditRepository;

    @Inject
    LayoutBlockAuditRepository layoutBlockAuditRepository;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Test
    void testAuditRowsForUpdateForEmptyList() {

        List<ExtractedTableRowAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post(ROW_PREFIX + "/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    void testAuditRowsForUpdate() {

        List<ExtractedTableRowAuditDto.Update> auditItems = List.of(
                new ExtractedTableRowAuditDto.Update(2, 1, "nta_table_id", String.valueOf(3),
                        String.valueOf(6)));
        List<ExtractedTableRowAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post(ROW_PREFIX + "/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            ExtractedTableRowAuditEntity extractedTableRowAuditEntity = extractedTableRowAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            ExtractedTableRowAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.tableId(), extractedTableRowAuditEntity.getTableId());
            Assertions.assertEquals(auditItem.rowId().shortValue(), extractedTableRowAuditEntity.getRowId());
            Assertions.assertEquals(auditItem.colName(), extractedTableRowAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), extractedTableRowAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, extractedTableRowAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), extractedTableRowAuditEntity.getPrevValue());
        }
    }

    @Test
    void testAuditBlocksForUpdateForEmptyList() {

        List<LayoutBlockAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post(BLOCK_PREFIX + "/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditUpdateItems.isEmpty());
    }

    @Test
    void testAuditBlocksForUpdate() {

        List<LayoutBlockAuditDto.Update> auditItems = List.of(
                new LayoutBlockAuditDto.Update(1, "tag", null,
                        "newTag"),
                new LayoutBlockAuditDto.Update(2, "comment", "oldComment",
                        "newComment"));
        List<LayoutBlockAuditDto.Response> auditUpdateItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post(BLOCK_PREFIX + "/audit-update")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditUpdateItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            LayoutBlockAuditEntity layoutBlockAuditEntity = layoutBlockAuditRepository.findById(
                    auditUpdateItems.get(i).id());
            LayoutBlockAuditDto.Update auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.blockId(), layoutBlockAuditEntity.getBlockId());
            Assertions.assertEquals(auditItem.colName(), layoutBlockAuditEntity.getColName());
            Assertions.assertEquals(auditItem.newValue(), layoutBlockAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.UPDATED, layoutBlockAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), layoutBlockAuditEntity.getPrevValue());
        }
    }

    @Test
    void testAuditBlocksForDeleteForEmptyList() {

        List<LayoutBlockAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(Collections.emptyList())
                .post(BLOCK_PREFIX + "/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertTrue(auditDeleteItems.isEmpty());
    }

    @Test
    void testAuditBlocksForDelete() {

        List<LayoutBlockAuditDto.Delete> auditItems = List.of(
                new LayoutBlockAuditDto.Delete(1, "tag", "tag1"),
                new LayoutBlockAuditDto.Delete(2, "comment", "comment1"));
        List<LayoutBlockAuditDto.Response> auditDeleteItems = given().when()
                .contentType(ContentType.JSON)
                .body(auditItems)
                .post(BLOCK_PREFIX + "/audit-delete")
                .then()
                .statusCode(200)
                .extract().body().as(new TypeRef<>() {
                });

        Assertions.assertEquals(auditItems.size(), auditDeleteItems.size());

        for (int i = 0; i < auditItems.size(); i++) {
            LayoutBlockAuditEntity layoutBlockAuditEntity = layoutBlockAuditRepository.findById(
                    auditDeleteItems.get(i).id());
            LayoutBlockAuditDto.Delete auditItem = auditItems.get(i);
            Assertions.assertEquals(auditItem.blockId(), layoutBlockAuditEntity.getBlockId());
            Assertions.assertEquals(auditItem.colName(), layoutBlockAuditEntity.getColName());
            Assertions.assertNull(layoutBlockAuditEntity.getNewValue());
            Assertions.assertEquals(AuditStatus.DELETED, layoutBlockAuditEntity.getAction());
            Assertions.assertEquals(auditItem.prevValue(), layoutBlockAuditEntity.getPrevValue());
        }
    }
}
