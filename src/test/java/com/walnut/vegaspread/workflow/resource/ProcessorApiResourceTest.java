package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.workflow.cloud.GcpCloudProvider;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.IdentifierKeyword;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.DocOutputDto;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import com.walnut.vegaspread.workflow.model.UpdateDocDto;
import com.walnut.vegaspread.workflow.repository.DocumentRepository;
import com.walnut.vegaspread.workflow.repository.EntityNameRepository;
import com.walnut.vegaspread.workflow.repository.IdentifierKeywordRepository;
import com.walnut.vegaspread.workflow.repository.IndustryRepository;
import com.walnut.vegaspread.workflow.repository.RegionRepository;
import com.walnut.vegaspread.workflow.repository.SpreadingTaskRepository;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.workflow.utils.Config.COMMON_CLIENT_NAME;
import static io.restassured.RestAssured.given;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@QuarkusTest
@TestHTTPEndpoint(ProcessorApiResource.class)
class ProcessorApiResourceTest {
    private static final String DEBUG_PATH = "debug/%s/%s";
    private static final String DOCUMENT_PREFIX = "/document";
    private static final String SPREAD_PREFIX = "/spread";
    @InjectMock
    GcpCloudProvider gcsService;
    @Inject
    Flyway flyway;
    @Inject
    IdentifierKeywordRepository identifierKeywordRepository;
    @Inject
    DocumentRepository documentRepository;
    @Inject
    EntityNameRepository entityNameRepository;
    @Inject
    IndustryRepository industryRepository;
    @Inject
    RegionRepository regionRepository;
    @Inject
    SpreadingTaskRepository spreadingTaskRepository;

    @BeforeEach
    void setUp() throws Exception {
        flyway.migrate();
    }

    @AfterEach
    void clean() {
        flyway.clean();
    }

    @Transactional
    List<IdentifierKeyword> addKeywords() {
        List<IdentifierKeyword> keywords = IntStream.range(1, 11).mapToObj(id -> IdentifierKeyword.builder()
                .kwText("kwText" + id)
                .category(id % 2 == 0 ? "category1" : "category2")
                .auditable(new Auditable("Name" + id))
                .build()).toList();
        identifierKeywordRepository.persist(keywords);
        return keywords;
    }

    @Transactional
    SpreadingTask createSpread() {
        int id = 1;
        EntityName entityName = EntityName.builder()
                .name("Entity" + id)
                .auditable(new Auditable("Name" + id))
                .build();
        entityNameRepository.persist(entityName);

        Industry industry = Industry.builder()
                .industryName("Industry" + id)
                .auditable(new Auditable("Name" + id))
                .build();
        industryRepository.persist(industry);

        Region region = Region.builder()
                .regionName("Region" + id)
                .auditable(new Auditable("Name" + id))
                .build();
        regionRepository.persist(region);

        SpreadingTask spread = SpreadingTask.builder()
                .clientName(COMMON_CLIENT_NAME)
                .entityName(entityName)
                .industry(industry)
                .region(region)
                .auditable(new Auditable("Name" + id))
                .lastModifiedBy("Name" + (id + 1))
                .lastModifiedTime(LocalDateTime.now())
                .build();
        spreadingTaskRepository.persist(spread);

        return spread;
    }

    @Transactional
    Document createDoc() {
        int id = 1;
        Document document = Document.builder()
                .period(LocalDate.of(2024, 12, id))
                .spreadLevel(SpreadLevelEnum.CONSOLIDATED)
                .fileName("Document" + id)
                .filePath("folder/Document" + id)
                .fileSize(1024 * (id + 1))
                .status(StatusEnum.DRAFT)
                .statusText("Status Text")
                .auditable(new Auditable("Name" + id))
                .isDigital(Boolean.TRUE)
                .lastModifiedBy("Name" + id)
                .lastModifiedTime(LocalDateTime.now())
                .spreadingTask(createSpread())
                .build();
        documentRepository.persist(document);
        return document;
    }

    @Transactional
    Document updateDoc(Document oldDoc) {
        Document dbDoc = documentRepository.findById(oldDoc.docId);
        dbDoc.status = oldDoc.status;
        documentRepository.persist(dbDoc);
        return dbDoc;
    }

    @Test
    void testUploadDebugFile() throws IOException {
        File file = File.createTempFile("test", ".json");
        UUID docId = UUID.randomUUID();
        String gcsPath = String.format(DEBUG_PATH, docId, file.getName());
        given().contentType(ContentType.MULTIPART)
                .multiPart("file", file)
                .when()
                .put("/doc/debug/{docId}", docId)
                .then()
                .statusCode(200);

        Mockito.verify(gcsService, Mockito.times(1)).upload(eq(gcsPath), any(Path.class));
    }

    @Test
    void testGetDebugLink() throws MalformedURLException {
        UUID docId = UUID.randomUUID();
        String fileName = "test.json";
        String gcsPath = String.format(DEBUG_PATH, docId, fileName);
        URL retUrl = new URL("http://localhost:8080/" + gcsPath);
        Mockito.when(gcsService.getLink(gcsPath, 2)).thenReturn(retUrl);

        UrlDto rspUrl = given().when()
                .queryParam("filename", fileName)
                .get("/doc/debug/link/{docId}", docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(UrlDto.class);

        Assertions.assertEquals(retUrl, rspUrl.url());

        Mockito.verify(gcsService, Mockito.times(1)).getLink(gcsPath, 2);
    }

    @Test
    void testListKeywordsForCategory() {
        List<IdentifierKeyword> keywords = addKeywords();
        String category = "category1";
        IdentifierKeyword[] rsp =
                given().when()
                        .get("/keyword/{category}", category)
                        .then()
                        .statusCode(200)
                        .extract()
                        .body()
                        .as(IdentifierKeyword[].class);

        List<IdentifierKeyword> filteredKeywords = keywords.stream()
                .filter(kw -> kw.category.equals(category))
                .toList();

        Assertions.assertEquals(filteredKeywords.size(), rsp.length);
        for (int i = 0; i < filteredKeywords.size(); i++) {
            Assertions.assertEquals(filteredKeywords.get(i).kwText, rsp[i].kwText);
        }
    }

    @Test
    void testUpdateDocumentMetadata() {
        Random rng = new Random();
        Document doc = createDoc();
        UpdateDocDto docDto = new UpdateDocDto(rng.nextInt(100), rng.nextInt(100), rng.nextInt(100),
                StatusEnum.PROCESSING);

        given().contentType(ContentType.JSON)
                .body(docDto)
                .when()
                .patch(DOCUMENT_PREFIX + "/{docId}", doc.docId)
                .then()
                .statusCode(200);

        Document dbDoc = documentRepository.findById(doc.docId);

        Assertions.assertEquals(doc.docId, dbDoc.docId);
        Assertions.assertEquals(docDto.dpi().shortValue(), dbDoc.dpi);
        Assertions.assertEquals(docDto.mappedItems().shortValue(), dbDoc.mappedItems);
        Assertions.assertEquals(docDto.ocrScore().byteValue(), dbDoc.ocrScore);
        Assertions.assertEquals(docDto.status(), dbDoc.status);
    }

    @Test
    void testGetDocFromDb() {
        Document doc = createDoc();
        DocOutputDto output = given().when()
                .get(DOCUMENT_PREFIX + "/{docId}", doc.docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(DocOutputDto.class);

        SpreadingTask task = doc.getSpreadingTask();
        Assertions.assertEquals(doc.docId, output.docId());
        Assertions.assertEquals(doc.period, output.period());
        Assertions.assertEquals(doc.spreadLevel, output.spreadLevel());
        Assertions.assertEquals(doc.dpi == null ? 0 : doc.dpi, output.dpi().shortValue());
        Assertions.assertEquals(0, output.extractedItems());
        Assertions.assertEquals(doc.mappedItems == null ? 0 : doc.mappedItems, output.mappedItems().shortValue());
        Assertions.assertEquals(doc.status, output.status());
        Assertions.assertEquals(doc.statusText, output.statusText());
        Assertions.assertEquals(task.getEntityName().getName(), output.entityName());
        Assertions.assertEquals(task.getEntityName().getEntityId(), output.entityId());
        Assertions.assertEquals(task.getIndustry().getIndustryName(), output.industryName());
        Assertions.assertEquals(task.getIndustry().getIndustryId(), output.industryId());
        Assertions.assertEquals(task.getRegion().getRegionName(), output.regionName());
        Assertions.assertEquals(task.getRegion().getRegionId(), output.regionId());
        Assertions.assertEquals(doc.fileName, output.docFileName());
        Assertions.assertEquals(doc.fileSize, output.docFileSize());
        Assertions.assertEquals(doc.auditable.createdBy, output.createdBy());
        Assertions.assertEquals(doc.isDigital ? "Digital" : "Scanned", output.documentType());
        Assertions.assertEquals(doc.ocrScore == null ? 100 : doc.ocrScore, output.ocrScore().byteValue());
        Assertions.assertEquals(doc.auditable.createdTime.toLocalDate(), output.createdTime().toLocalDate());
        Assertions.assertEquals(doc.lastModifiedBy, output.lastModifiedBy());
        Assertions.assertEquals(doc.lastModifiedTime.toLocalDate(), output.lastModifiedTime().toLocalDate());
        Assertions.assertEquals(doc.spreadingTask.clientName, output.clientName());
    }

    @Test
    void testGetDocLink() throws MalformedURLException {
        Document doc = createDoc();
        URL retUrl = new URL("http://localhost:8080/" + doc.filePath);
        Mockito.when(gcsService.getLink(doc.filePath, 30)).thenReturn(retUrl);

        UrlDto output = given().when()
                .get(DOCUMENT_PREFIX + "/link/{docId}", doc.docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(UrlDto.class);

        Assertions.assertEquals(retUrl, output.url());
        Mockito.verify(gcsService, Mockito.times(1)).getLink(doc.filePath, 30);
    }

    @Test
    void testFilterDocuments() {
        Document doc = createDoc();
        doc.status = StatusEnum.COMPLETED;
        doc = updateDoc(doc);
        UUID[] output = given().when()
                .queryParam("filterColumn", "entity_name_id")
                .queryParam("filterValue", 1)
                .get(DOCUMENT_PREFIX + "/filter")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(UUID[].class);
        Assertions.assertEquals(1, output.length);
        Assertions.assertEquals(doc.docId, output[0]);

        UUID[] zeroOutput = given().when()
                .queryParam("filterColumn", "entity_name_id")
                .queryParam("filterValue", 2)
                .get(DOCUMENT_PREFIX + "/filter")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(UUID[].class);
        Assertions.assertEquals(0, zeroOutput.length);
    }

    @Test
    void testGetSpread() {
        Document doc = createDoc();
        DocOutputDto output = given().when()
                .get(SPREAD_PREFIX + "/{spreadId}", doc.spreadingTask.spreadId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(DocOutputDto.class);

        SpreadingTask task = doc.spreadingTask;
        Assertions.assertEquals(task.spreadId, output.spreadId());
        Assertions.assertEquals(doc.period, output.period());
        Assertions.assertEquals(doc.spreadLevel, output.spreadLevel());
        Assertions.assertEquals(doc.dpi == null ? 0 : doc.dpi, output.dpi().shortValue());
        Assertions.assertEquals(0, output.extractedItems());
        Assertions.assertEquals(doc.mappedItems == null ? 0 : doc.mappedItems, output.mappedItems().shortValue());
        Assertions.assertEquals(doc.status, output.status());
        Assertions.assertEquals(doc.statusText, output.statusText());
        Assertions.assertEquals(task.getEntityName().getName(), output.entityName());
        Assertions.assertEquals(task.getIndustry().getIndustryName(), output.industryName());
        Assertions.assertEquals(task.getRegion().getRegionName(), output.regionName());
        Assertions.assertEquals(doc.docId, output.docId());
        Assertions.assertEquals(doc.fileName, output.docFileName());
        Assertions.assertEquals(doc.fileSize, output.docFileSize());
        Assertions.assertEquals(doc.auditable.createdBy, output.createdBy());
        Assertions.assertEquals(doc.isDigital ? "Digital" : "Scanned", output.documentType());
        Assertions.assertEquals(doc.ocrScore == null ? 100 : doc.ocrScore, output.ocrScore().byteValue());
        Assertions.assertEquals(doc.auditable.createdTime.toLocalDate(), output.createdTime().toLocalDate());
        Assertions.assertEquals(doc.lastModifiedBy, output.lastModifiedBy());
        Assertions.assertEquals(doc.lastModifiedTime.toLocalDate(), output.lastModifiedTime().toLocalDate());
        Assertions.assertEquals(task.clientName, output.clientName());
    }

    @Test
    void testGetSpreadForNullTask() {

        Map<String, String> error = given().when()
                .get(SPREAD_PREFIX + "/{spreadId}", 1)
                .then()
                .statusCode(500)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });

        Assertions.assertTrue(error.get("details").contains(" java.lang.IllegalArgumentException"));
    }
}
