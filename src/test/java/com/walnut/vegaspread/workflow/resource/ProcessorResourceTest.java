package com.walnut.vegaspread.workflow.resource;

import com.walnut.vegaspread.common.model.extraction.UrlDto;
import com.walnut.vegaspread.common.model.workflow.StatusEnum;
import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.workflow.cloud.GcpCloudProvider;
import com.walnut.vegaspread.workflow.entity.Auditable;
import com.walnut.vegaspread.workflow.entity.Document;
import com.walnut.vegaspread.workflow.entity.EntityName;
import com.walnut.vegaspread.workflow.entity.IdentifierKeyword;
import com.walnut.vegaspread.workflow.entity.Industry;
import com.walnut.vegaspread.workflow.entity.Region;
import com.walnut.vegaspread.workflow.entity.SpreadingTask;
import com.walnut.vegaspread.workflow.model.KeywordDto;
import com.walnut.vegaspread.workflow.model.SpreadLevelEnum;
import com.walnut.vegaspread.workflow.model.StageEnum;
import com.walnut.vegaspread.workflow.repository.DocumentRepository;
import com.walnut.vegaspread.workflow.repository.EntityNameRepository;
import com.walnut.vegaspread.workflow.repository.IdentifierKeywordRepository;
import com.walnut.vegaspread.workflow.repository.IndustryRepository;
import com.walnut.vegaspread.workflow.repository.RegionRepository;
import com.walnut.vegaspread.workflow.repository.SpreadingTaskRepository;
import com.walnut.vegaspread.workflow.repository.WiseServiceRepository;
import com.walnut.vegaspread.workflow.service.ExchangeService;
import com.walnut.vegaspread.workflow.service.ProcessorService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import jakarta.ws.rs.core.Response;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.Constants.DEFAULT_CLIENT_NAME;
import static com.walnut.vegaspread.common.utils.Constants.SUMMARY_TEMPLATE_EXCEL_PATH;
import static com.walnut.vegaspread.common.utils.TestSetup.startMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.stopMockServer;
import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@QuarkusTest
@TestHTTPEndpoint(ProcessorResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProcessorResourceTest {
    private static final String XLSX_MIME_TYPE = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    @Inject
    Flyway flyway;
    @InjectMock
    GcpCloudProvider gcsService;
    @InjectMock
    ProcessorService processorService;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    EntityNameRepository entityNameRepository;
    @Inject
    IndustryRepository industryRepository;
    @Inject
    RegionRepository regionRepository;
    @Inject
    SpreadingTaskRepository spreadingTaskRepository;
    @Inject
    DocumentRepository documentRepository;
    @Inject
    IdentifierKeywordRepository identifierKeywordRepository;
    @Inject
    WiseServiceRepository wiseServiceRepository;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
        startMockServer();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
        stopMockServer();
    }

    @Transactional
    List<Document> createDocs() {
        List<EntityName> entityNames = IntStream.range(1, 2).mapToObj(id -> EntityName.builder()
                .name("Entity" + id)
                .auditable(new Auditable("Name" + id))
                .build()).toList();
        entityNameRepository.persist(entityNames);

        List<Industry> industries = IntStream.range(1, 2).mapToObj(id -> Industry.builder()
                .industryName("Industry" + id)
                .auditable(new Auditable("Name" + id))
                .build()).toList();
        industryRepository.persist(industries);

        List<Region> regions = IntStream.range(1, 2).mapToObj(id -> Region.builder()
                .regionName("Region" + id)
                .auditable(new Auditable("Name" + id))
                .build()).toList();
        regionRepository.persist(regions);

        List<SpreadingTask> spreads = IntStream.range(0, 1).mapToObj(id -> SpreadingTask.builder()
                .clientName(id == 0 ? "Client0" : "Client1")
                .entityName(entityNames.get(id))
                .industry(industries.get(id))
                .region(regions.get(id))
                .auditable(new Auditable("Name" + id))
                .lastModifiedBy("Name" + (id + 1))
                .lastModifiedTime(LocalDateTime.now())
                .build()).toList();
        spreadingTaskRepository.persist(spreads);

        List<Document> documents = IntStream.range(1, 2).mapToObj(id -> Document.builder()
                .period(LocalDate.of(2024, 12, id))
                .spreadLevel(id % 2 == 0 ? SpreadLevelEnum.CONSOLIDATED : SpreadLevelEnum.STANDALONE)
                .fileName("Document" + id)
                .filePath("folder/Document" + id)
                .fileSize(1024 * (id + 1))
                .status(id % 2 == 0 ? StatusEnum.PROCESSING : StatusEnum.CREATED)
                .statusText("Status Text")
                .auditable(new Auditable("Name" + id))
                .isDigital(id % 3 == 0 ? Boolean.TRUE : Boolean.FALSE)
                .lastModifiedBy("Name" + id)
                .lastModifiedTime(LocalDateTime.now())
                .spreadingTask(spreads.get(id - 1))
                .build()).toList();
        documentRepository.persist(documents);

        return documents;
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.UPLOAD_FILE)
    void testStartProcess() {
        List<Document> docs = createDocs();
        UUID docId = docs.get(0).docId;

        when(processorService.startProcess(eq(docId), eq(StageEnum.Process.ROTATION), any())).thenReturn(
                Response.Status.OK);

        given().contentType(ContentType.JSON).when().post("/process/doc/{docId}", docId).then().statusCode(200);

        verify(processorService, times(1)).startProcess(eq(docId), eq(StageEnum.Process.ROTATION), any());
    }

    @Test
    @TestSecurity(user = "Name0")
    void testGetSummaryTemplateLink() throws MalformedURLException {
        String clientName = "Client0";

        String gcsPath = String.format(SUMMARY_TEMPLATE_EXCEL_PATH, clientName.replaceAll("\\s+", "_").toLowerCase(),
                DEFAULT_CLIENT_NAME.replaceAll("\\s+", "_").toLowerCase());
        String link = "https://storage.googleapis.com/bucket/" + gcsPath;
        when(gcsService.getLink(gcsPath, 2)).thenReturn(new URL(link));

        UrlDto url = given().when().get("/doc/summary/template/excel/link/{clientName}", clientName)
                .then().statusCode(200).extract().body().as(UrlDto.class);

        Assertions.assertEquals(link, url.url().toString());
        verify(gcsService, times(1)).getLink(gcsPath, 2);
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.SUPERADMIN)
    void testUploadSummaryTemplateExcelLink() throws IOException {
        String clientName = "Client0";

        String gcsPath = String.format(SUMMARY_TEMPLATE_EXCEL_PATH, clientName.replaceAll("\\s+", "_").toLowerCase(),
                DEFAULT_CLIENT_NAME.replaceAll("\\s+", "_").toLowerCase());
        String link = "https://storage.googleapis.com/bucket/" + gcsPath;
        when(gcsService.getSignedPutUrl(gcsPath, 3, XLSX_MIME_TYPE)).thenReturn(new URL(link));

        UrlDto url = given().when().get("/doc/summary/template/excel/upload-url/{clientName}", clientName)
                .then().statusCode(200).extract().body().as(UrlDto.class);

        Assertions.assertEquals(link, url.url().toString());
        verify(gcsService, times(1)).getSignedPutUrl(gcsPath, 3, XLSX_MIME_TYPE);
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.ADMIN)
    void testAddKeywords() {
        List<KeywordDto> keywordDtos = List.of(new KeywordDto("text", "category"),
                new KeywordDto("text2", "category"));

        given().contentType(ContentType.JSON).body(keywordDtos).when().post("/keyword").then().statusCode(200);

        List<IdentifierKeyword> dbKeywords = identifierKeywordRepository.listAll();
        Assertions.assertEquals(keywordDtos.size(), dbKeywords.size());
        for (int i = 0; i < keywordDtos.size(); i++) {
            Assertions.assertEquals(keywordDtos.get(i).kwText(), dbKeywords.get(i).kwText);
            Assertions.assertEquals(keywordDtos.get(i).category(), dbKeywords.get(i).category);
        }

        verify(exchangeService, times(1)).usernameToName("Name0");
    }

    @Transactional
    List<IdentifierKeyword> addKeywords() {
        List<IdentifierKeyword> keywords = IntStream.range(1, 11).mapToObj(id -> IdentifierKeyword.builder()
                .kwText("kwText" + id)
                .category(id % 2 == 0 ? "category1" : "category2")
                .auditable(new Auditable("Name" + id))
                .build()).toList();
        identifierKeywordRepository.persist(keywords);
        return keywords;
    }

    @Test
    @TestSecurity(user = "Name0", roles = Roles.LABEL_DATA)
    void testListKeywordsForCategory() {
        List<IdentifierKeyword> keywords = addKeywords();

        IdentifierKeyword[] response = given().when().get("/keyword/{category}", "category1")
                .then().statusCode(200)
                .extract().body().as(IdentifierKeyword[].class);

        List<IdentifierKeyword> expectedKw = keywords.stream()
                .filter(kw -> Objects.equals(kw.category, "category1"))
                .toList();
        Assertions.assertEquals(expectedKw.size(), response.length);
        for (int i = 0; i < expectedKw.size(); i++) {
            Assertions.assertEquals(expectedKw.get(i).kwText, response[i].kwText);
            Assertions.assertEquals(expectedKw.get(i).category, response[i].category);
        }

        verify(exchangeService, times(1)).getUsernameMapping(any());
    }
}
