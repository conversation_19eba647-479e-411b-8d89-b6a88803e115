package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbHeader;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;
import static org.mockito.ArgumentMatchers.eq;

@QuarkusTest
@TestHTTPEndpoint(TableResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class TableResourceTest {

    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId) {
        List<LayoutBlockEntity> blocks = IntStream.range(0, 5).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag("tag" + id)
                .blockType(BlockTypeEnum.TABLE)
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        List<TableHeaderEntity> headers = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableHeaderEntity> blockHeaders = IntStream.range(0, 3)
                    .mapToObj(id -> TableHeaderEntity.builder()
                            .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .text("header" + id)
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .pos(id)
                            .build())
                    .toList();
            headers.addAll(blockHeaders);
            block.setTableHeaders(blockHeaders);
        }
        tableHeaderRepository.persist(headers);

        List<TableRowEntity> rows = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableRowEntity> blockRows = IntStream.range(0, 3)
                    .mapToObj(id -> TableRowEntity.builder()
                            .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .cellsText(List.of("row" + id, "row" + id))
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .comment("comment" + id)
                            .parentText("parent" + id)
                            .pos(id)
                            .build())
                    .toList();
            rows.addAll(blockRows);
            block.setTableRows(blockRows);
        }
        tableRowRepository.persist(rows);
        return blocks;
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    void testUpdateRowsComments() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        List<DbRow.UpdateCommentDto> updateCommentDtos = blocks.stream()
                .flatMap(block -> block.getTableRows().stream())
                .map(row -> new DbRow.UpdateCommentDto(row.getTableRowPkId().getTableId(),
                        row.getTableRowPkId().getRowId(), "new comment"))
                .toList();
        Assertions.assertFalse(updateCommentDtos.isEmpty());

        given().contentType(ContentType.JSON)
                .body(updateCommentDtos)
                .when()
                .patch("/rows/comments")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (LayoutBlockEntity dbBlock : dbBlocks) {
            List<DbRow.UpdateCommentDto> blockCommentDtos = updateCommentDtos.stream()
                    .filter(dto -> dto.tableId() == dbBlock.getBlockId())
                    .toList();
            for (int i = 0; i < dbBlock.getTableRows().size(); i++) {
                TableRowEntity dbRow = dbBlock.getTableRows().get(i);
                DbRow.UpdateCommentDto dto = blockCommentDtos.get(i);

                Assertions.assertEquals(dto.tableId(), dbBlock.getBlockId());
                Assertions.assertEquals(dto.rowId(), (int) dbRow.getTableRowPkId().getRowId());
                Assertions.assertEquals(dto.comment(), dbRow.getComment());
            }
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.EDIT_FIGURES)
    void testUpdateRowsText() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        List<DbRow.UpdateTextDto> updateTextDtos = blocks.stream()
                .flatMap(block -> block.getTableRows().stream())
                .map(row -> new DbRow.UpdateTextDto(row.getTableRowPkId().getTableId(),
                        row.getTableRowPkId().getRowId(), List.of("new text", "new text", "new text")))
                .toList();
        Assertions.assertFalse(updateTextDtos.isEmpty());

        given().contentType(ContentType.JSON)
                .body(updateTextDtos)
                .when()
                .patch("/text")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (LayoutBlockEntity dbBlock : dbBlocks) {
            List<DbRow.UpdateTextDto> blockTextDtos = updateTextDtos.stream()
                    .filter(dto -> dto.tableId() == dbBlock.getBlockId())
                    .toList();
            for (int i = 0; i < dbBlock.getTableRows().size(); i++) {
                TableRowEntity dbRow = dbBlock.getTableRows().get(i);
                DbRow.UpdateTextDto dto = blockTextDtos.get(i);

                Assertions.assertEquals(dto.tableId(), dbBlock.getBlockId());
                Assertions.assertEquals(dto.rowId(), (int) dbRow.getTableRowPkId().getRowId());
                Assertions.assertEquals(dto.cellsText(), dbRow.getCellsText());
            }
        }
        Mockito.verify(exchangeService, Mockito.times(1)).auditRows(Mockito.any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.EDIT_FIGURES)
    void testUpdateHeaderText() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        List<DbHeader.UpdateHeaderTextDto> updateHeaderTextDtos = blocks.stream()
                .flatMap(block -> block.getTableHeaders().stream())
                .map(header -> new DbHeader.UpdateHeaderTextDto(header.getTableHeaderPkId().getTableId(),
                        header.getTableHeaderPkId().getHeaderId(), "new text"))
                .toList();
        Assertions.assertFalse(updateHeaderTextDtos.isEmpty());

        given().contentType(ContentType.JSON)
                .body(updateHeaderTextDtos)
                .when()
                .patch("/header-text")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (LayoutBlockEntity dbBlock : dbBlocks) {
            List<DbHeader.UpdateHeaderTextDto> blockHeaderDtos = updateHeaderTextDtos.stream()
                    .filter(dto -> dto.tableId() == dbBlock.getBlockId())
                    .toList();
            for (int i = 0; i < dbBlock.getTableHeaders().size(); i++) {
                TableHeaderEntity dbHeader = dbBlock.getTableHeaders().get(i);
                DbHeader.UpdateHeaderTextDto dto = blockHeaderDtos.get(i);

                Assertions.assertEquals(dto.tableId(), dbBlock.getBlockId());
                Assertions.assertEquals(dto.headerId(), dbHeader.getPos());
                Assertions.assertEquals(dto.text(), dbHeader.getText());
            }
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.TAG_TABLE)
    void testUpdateRowsFyHeaders() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        List<DbRow.UpdateFyHeaderDto> updateFyHeaderDtos = blocks.stream()
                .flatMap(block -> block.getTableRows().stream())
                .map(row -> new DbRow.UpdateFyHeaderDto(row.getTableRowPkId().getTableId(),
                        row.getTableRowPkId().getRowId(),
                        List.of(1, 2)))
                .toList();
        Assertions.assertFalse(updateFyHeaderDtos.isEmpty());

        given().contentType(ContentType.JSON)
                .body(updateFyHeaderDtos)
                .queryParams("spreadId", 1)
                .when()
                .patch("/fyheaders")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (LayoutBlockEntity dbBlock : dbBlocks) {
            List<DbRow.UpdateFyHeaderDto> blockFyHeaderDtos = updateFyHeaderDtos.stream()
                    .filter(dto -> dto.tableId() == dbBlock.getBlockId())
                    .toList();
            for (int i = 0; i < dbBlock.getTableRows().size(); i++) {
                TableRowEntity dbRow = dbBlock.getTableRows().get(i);
                DbRow.UpdateFyHeaderDto dto = blockFyHeaderDtos.get(i);

                Assertions.assertEquals(dto.tableId(), dbBlock.getBlockId());
                Assertions.assertEquals(dto.rowId(), (int) dbRow.getTableRowPkId().getRowId());
                Assertions.assertEquals(dto.headerIds(), dbRow.getHeaderIds());
            }
        }
        Mockito.verify(exchangeService, Mockito.times(1)).auditRows(Mockito.any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    void testUpdateCoa() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        List<DbRow.UpdateCoaDto> updateCoaDtos = blocks.stream()
                .flatMap(block -> block.getTableRows().stream())
                .map(row -> new DbRow.UpdateCoaDto(row.getTableRowPkId().getTableId(), row.getTableRowPkId()
                        .getRowId(),
                        new Random().nextInt(2, 100), new Random().nextBoolean()))
                .toList();
        Assertions.assertFalse(updateCoaDtos.isEmpty());

        given().contentType(ContentType.JSON)
                .body(updateCoaDtos)
                .queryParams("spreadId", 1)
                .when()
                .patch("/coamap")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (LayoutBlockEntity dbBlock : dbBlocks) {
            List<DbRow.UpdateCoaDto> blockCoaDtos = updateCoaDtos.stream()
                    .filter(dto -> dto.tableId() == dbBlock.getBlockId())
                    .toList();

            for (int i = 0; i < dbBlock.getTableRows().size(); i++) {
                TableRowEntity dbRow = dbBlock.getTableRows().get(i);
                Optional<ExtractedTableRowCoaDataJoinEntity> extractedTableRowCoaDataJoinEntityOptional =
                        extractedRowCoaDataRepository.findByRowOptional(
                                dbRow.getTableRowPkId().getTableId(),
                                dbRow.getTableRowPkId().getRowId());
                DbRow.UpdateCoaDto dto = blockCoaDtos.get(i);

                Assertions.assertEquals(dto.tableId(), dbBlock.getBlockId());
                Assertions.assertEquals(dto.rowId(), (int) dbRow.getTableRowPkId().getRowId());
                if (extractedTableRowCoaDataJoinEntityOptional.isPresent()) {
                    CoaDataEntity coaDataEntity = coaDataRepository.findById(
                            extractedTableRowCoaDataJoinEntityOptional.get()
                                    .getExtractedTableRowCoaDataPkId()
                                    .getCoaDataId());
                    Assertions.assertEquals(dto.useCoa(), coaDataEntity.getUseCoa());
                    Assertions.assertEquals(dto.coaId(), coaDataEntity.getCoaId());
                    Assertions.assertEquals(dto.useCoa(), coaDataEntity.getUseCoa());
                    Assertions.assertNull(extractedTableRowCoaDataJoinEntityOptional.get().getExplainability());
                }
            }
        }

        Mockito.verify(exchangeService, Mockito.times(1)).auditExtractedRowCoaDataCreate(Mockito.any(), eq(false));
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.TAG_TABLE)
    void testUpdateNtaLink() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity fsTable = blocks.get(0);
        List<DbRow.UpdateNtaLinkDto> updateNtaLinkDtos = fsTable.getTableRows().stream()
                .map(row -> new DbRow.UpdateNtaLinkDto(row.getTableRowPkId().getTableId(),
                        row.getTableRowPkId().getRowId(), blocks.get(0).getBlockId()))
                .toList();
        Assertions.assertFalse(updateNtaLinkDtos.isEmpty());

        given().contentType(ContentType.JSON)
                .body(updateNtaLinkDtos)
                .when()
                .patch("/nta-link")
                .then()
                .statusCode(200);

        LayoutBlockEntity dbBlock = layoutBlockRepository.findById(fsTable.getBlockId());

        List<DbRow.UpdateNtaLinkDto> blockNtaLinkDtos = updateNtaLinkDtos.stream()
                .filter(dto -> dto.fsTableId() == dbBlock.getBlockId())
                .toList();
        for (int i = 0; i < dbBlock.getTableRows().size(); i++) {
            TableRowEntity dbRow = dbBlock.getTableRows().get(i);
            DbRow.UpdateNtaLinkDto dto = blockNtaLinkDtos.get(i);

            Assertions.assertEquals(dto.fsTableId(), dbBlock.getBlockId());
            Assertions.assertEquals(dto.rowId(), (int) dbRow.getTableRowPkId().getRowId());
            Assertions.assertEquals(dto.ntaTableId(), dbRow.getNtaTable().getBlockId());
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testInsertHeader() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity block = blocks.get(1);
        List<TableHeaderEntity> headersBeforeUpdate = block.getTableHeaders()
                .stream()
                .sorted(Comparator.comparingInt(TableHeaderEntity::getPos))
                .toList();
        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();
        byte maxHeaderIdBeforeUpdate = headersBeforeUpdate.stream()
                .map(tableHeaderEntity -> tableHeaderEntity.getTableHeaderPkId().getHeaderId())
                .max(Integer::compare).orElse((byte) 0);

        DbHeader.InsertHeaderDto insertHeaderDto = new DbHeader.InsertHeaderDto(block.getBlockId(), 1, "New Header");
        given().contentType(ContentType.JSON)
                .body(insertHeaderDto)
                .when()
                .post("/header")
                .then()
                .statusCode(200);

        List<TableHeaderEntity> updatedHeadersForBlock = tableHeaderRepository.getForTableSortByPos(block.getBlockId());

        for (int i = 0; i < updatedHeadersForBlock.size(); i++) {
            if (i < insertHeaderDto.pos()) {
                Assertions.assertEquals(headersBeforeUpdate.get(i).getPos(), updatedHeadersForBlock.get(i).getPos());
            } else if (i == insertHeaderDto.pos()) {

                Assertions.assertEquals(insertHeaderDto.pos(), updatedHeadersForBlock.get(i).getPos());
                Assertions.assertEquals(insertHeaderDto.text(), updatedHeadersForBlock.get(i).getText());
                Assertions.assertEquals(maxHeaderIdBeforeUpdate + 1,
                        updatedHeadersForBlock.get(i).getTableHeaderPkId().getHeaderId().intValue());
            } else {
                Assertions.assertEquals(headersBeforeUpdate.get(i - 1).getPos() + 1,
                        updatedHeadersForBlock.get(i).getPos());
            }
        }

        List<TableRowEntity> updatedTableRows = tableRowRepository.getForTableSortByPos(block.getBlockId());
        for (int i = 0; i < updatedTableRows.size(); i++) {
            TableRowEntity rowBeforeUpdate = rowsBeforeUpdate.get(i);
            TableRowEntity rowAfterUpdate = updatedTableRows.get(i);
            for (int j = 0; j < rowAfterUpdate.getCellsText().size(); j++) {
                if (j < insertHeaderDto.pos()) {
                    Assertions.assertEquals(rowBeforeUpdate.getCellsText().get(j),
                            rowAfterUpdate.getCellsText().get(j));
                } else if (j == insertHeaderDto.pos()) {
                    Assertions.assertEquals(StringUtils.EMPTY, rowAfterUpdate.getCellsText().get(j));
                } else {
                    Assertions.assertEquals(rowBeforeUpdate.getCellsText().get(j - 1),
                            rowAfterUpdate.getCellsText().get(j));
                }
            }
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testInsertHeaderWithPosMoreThanSize() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity block = blocks.get(1);
        List<TableHeaderEntity> headersBeforeUpdate = block.getTableHeaders()
                .stream()
                .sorted(Comparator.comparingInt(TableHeaderEntity::getPos))
                .toList();
        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();
        byte maxHeaderIdBeforeUpdate = headersBeforeUpdate.stream()
                .map(tableHeaderEntity -> tableHeaderEntity.getTableHeaderPkId().getHeaderId())
                .max(Integer::compare).orElse((byte) 0);
        DbHeader.InsertHeaderDto insertHeaderDto = new DbHeader.InsertHeaderDto(block.getBlockId(),
                headersBeforeUpdate.size() + 2, "New Header");
        given().contentType(ContentType.JSON)
                .body(insertHeaderDto)
                .when()
                .post("/header")
                .then()
                .statusCode(200);
        List<TableHeaderEntity> updatedHeadersForBlock = tableHeaderRepository.getForTableSortByPos(block.getBlockId());
        for (int i = 0; i < updatedHeadersForBlock.size(); i++) {
            if (i < headersBeforeUpdate.size()) {
                Assertions.assertEquals(headersBeforeUpdate.get(i).getPos(), updatedHeadersForBlock.get(i).getPos());
            } else {
                Assertions.assertEquals(maxHeaderIdBeforeUpdate + 1,
                        updatedHeadersForBlock.get(i).getTableHeaderPkId().getHeaderId().intValue());
                Assertions.assertEquals(insertHeaderDto.text(), updatedHeadersForBlock.get(i).getText());
                Assertions.assertEquals(i, updatedHeadersForBlock.get(i).getPos());
            }
        }
        List<TableRowEntity> updatedTableRows = tableRowRepository.getForTableSortByPos(block.getBlockId());
        for (int i = 0; i < updatedTableRows.size(); i++) {
            TableRowEntity rowBeforeUpdate = rowsBeforeUpdate.get(i);
            TableRowEntity rowAfterUpdate = updatedTableRows.get(i);
            for (int j = 0; j < rowAfterUpdate.getCellsText().size(); j++) {
                if (j < rowBeforeUpdate.getCellsText().size()) {
                    Assertions.assertEquals(rowBeforeUpdate.getCellsText().get(j),
                            rowAfterUpdate.getCellsText().get(j));
                } else {
                    Assertions.assertEquals(StringUtils.EMPTY, rowAfterUpdate.getCellsText().get(j));
                }
            }
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testInsertRow() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity block = blocks.get(1);
        List<TableHeaderEntity> headersBeforeUpdate = block.getTableHeaders()
                .stream()
                .sorted(Comparator.comparingInt(TableHeaderEntity::getPos))
                .toList();
        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();
        short maxRowIdBeforeUpdate = rowsBeforeUpdate.stream()
                .map(tableRowEntity -> tableRowEntity.getTableRowPkId().getRowId())
                .max(Integer::compare).orElse((short) 0);

        DbRow.InsertRowDto insertRowDto = new DbRow.InsertRowDto(block.getBlockId(), "New Row", 0);
        given().contentType(ContentType.JSON)
                .body(insertRowDto)
                .when()
                .post("/rows")
                .then()
                .statusCode(200);

        List<TableRowEntity> updatedRowsForBlock = tableRowRepository.getForTableSortByPos(block.getBlockId());

        for (int i = 0; i < updatedRowsForBlock.size(); i++) {
            if (i < insertRowDto.pos()) {
                Assertions.assertEquals(rowsBeforeUpdate.get(i), updatedRowsForBlock.get(i));
            } else if (i == insertRowDto.pos()) {
                List<String> cellsText = new ArrayList<>(List.of(insertRowDto.text()));
                cellsText.addAll(Stream.generate(() -> StringUtils.EMPTY)
                        .limit((headersBeforeUpdate.size() - 1))
                        .toList());

                Assertions.assertEquals(insertRowDto.pos(), updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(StringUtils.EMPTY, updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(cellsText, updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(new Bbox(0, 0, 0, 0), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(0, updatedRowsForBlock.get(i).getScore().intValue());
                Assertions.assertNull(updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(StringUtils.EMPTY, updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(block.getBlockId(), updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(maxRowIdBeforeUpdate + 1,
                        updatedRowsForBlock.get(i).getTableRowPkId().getRowId().intValue());
            } else {

                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getPos() + 1, updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getParentText(),
                        updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getCellsText(),
                        updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getBbox(), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getScore(), updatedRowsForBlock.get(i).getScore());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getNtaTable(),
                        updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getComment(),
                        updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getLayoutBlock().getBlockId(),
                        updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i - 1).getTableRowPkId(),
                        updatedRowsForBlock.get(i).getTableRowPkId());
            }
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testInsertRowWithPosMoreThanSize() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity block = blocks.get(1);
        List<TableHeaderEntity> headersBeforeUpdate = block.getTableHeaders()
                .stream()
                .sorted(Comparator.comparingInt(TableHeaderEntity::getPos))
                .toList();
        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();
        short maxRowIdBeforeUpdate = rowsBeforeUpdate.stream()
                .map(tableRowEntity -> tableRowEntity.getTableRowPkId().getRowId())
                .max(Integer::compare).orElse((short) 0);

        DbRow.InsertRowDto insertRowDto = new DbRow.InsertRowDto(block.getBlockId(), "New Row",
                rowsBeforeUpdate.size() + 2);
        given().contentType(ContentType.JSON)
                .body(insertRowDto)
                .when()
                .post("/rows")
                .then()
                .statusCode(200);

        List<TableRowEntity> updatedRowsForBlock = tableRowRepository.getForTableSortByPos(block.getBlockId());
        for (int i = 0; i < updatedRowsForBlock.size(); i++) {
            if (i < rowsBeforeUpdate.size()) {
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getTableRowPkId(),
                        updatedRowsForBlock.get(i).getTableRowPkId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getNtaTable(),
                        updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getParentText(),
                        updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getCellsText(),
                        updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getScore(),
                        updatedRowsForBlock.get(i).getScore());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getComment(),
                        updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getBbox(),
                        updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getLayoutBlock().getBlockId(),
                        updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getCoaData(),
                        updatedRowsForBlock.get(i).getCoaData());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getPos(), updatedRowsForBlock.get(i).getPos());
            } else {
                List<String> cellsText = new ArrayList<>(List.of(insertRowDto.text()));
                cellsText.addAll(Stream.generate(() -> StringUtils.EMPTY)
                        .limit((headersBeforeUpdate.size() - 1))
                        .toList());

                Assertions.assertEquals(rowsBeforeUpdate.size(), updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(StringUtils.EMPTY, updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(cellsText, updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(new Bbox(0, 0, 0, 0), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(0, updatedRowsForBlock.get(i).getScore().intValue());
                Assertions.assertNull(updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(StringUtils.EMPTY, updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(block.getBlockId(), updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(maxRowIdBeforeUpdate + 1,
                        updatedRowsForBlock.get(i).getTableRowPkId().getRowId().intValue());
            }
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testDeleteHeader() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);

        LayoutBlockEntity block = blocks.get(1);
        TableHeaderEntity headerForDeletion = block.getTableHeaders().get(1);

        block.getTableRows().forEach(row -> {
            row.setHeaderIds(List.of(headerForDeletion.getTableHeaderPkId().getTableId()));
        });

        List<TableHeaderEntity> headersBeforeUpdate = block.getTableHeaders()
                .stream()
                .sorted(Comparator.comparingInt(TableHeaderEntity::getPos))
                .toList();
        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();

        Integer tableIdForDeletion = headerForDeletion.getTableHeaderPkId().getTableId();
        Integer headerIdForDeletion = Integer.valueOf(
                headerForDeletion.getTableHeaderPkId().getHeaderId());
        DbHeader.DeleteHeaderDto deleteHeaderDto = new DbHeader.DeleteHeaderDto(tableIdForDeletion,
                headerIdForDeletion);
        given().contentType(ContentType.JSON)
                .body(deleteHeaderDto)
                .when()
                .delete("/header")
                .then()
                .statusCode(200);

        List<TableHeaderEntity> updatedHeadersForBlock = tableHeaderRepository.getForTableSortByPos(tableIdForDeletion);

        Assertions.assertFalse(updatedHeadersForBlock.stream()
                .map(tableHeaderEntity -> tableHeaderEntity.getTableHeaderPkId().getHeaderId())
                .toList()
                .contains(headerIdForDeletion.byteValue()));

        for (int i = 0; i < updatedHeadersForBlock.size(); i++) {
            if (i < headerForDeletion.getPos()) {
                Assertions.assertEquals(headersBeforeUpdate.get(i).getPos(), updatedHeadersForBlock.get(i).getPos());
            } else {
                Assertions.assertEquals(headersBeforeUpdate.get(i + 1).getPos() - 1,
                        updatedHeadersForBlock.get(i).getPos());
            }
        }

        List<TableRowEntity> updatedTableRows = tableRowRepository.getForTableSortByPos(block.getBlockId());
        for (int i = 0; i < updatedTableRows.size(); i++) {
            TableRowEntity rowBeforeUpdate = rowsBeforeUpdate.get(i);
            TableRowEntity rowAfterUpdate = updatedTableRows.get(i);
            Assertions.assertFalse(rowAfterUpdate.getHeaderIds().contains(headerIdForDeletion));
            for (int j = 0; j < rowAfterUpdate.getCellsText().size(); j++) {
                if (j < headerForDeletion.getPos()) {
                    Assertions.assertEquals(rowBeforeUpdate.getCellsText().get(j),
                            rowAfterUpdate.getCellsText().get(j));
                } else {
                    Assertions.assertEquals(rowBeforeUpdate.getCellsText().get(j + 1),
                            rowAfterUpdate.getCellsText().get(j));
                }
            }
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testDeleteRow() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity block = blocks.get(1);

        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();

        TableRowEntity rowForDeletion = block.getTableRows().get(1);
        Integer tableIdForDeletion = rowForDeletion.getTableRowPkId().getTableId();
        Integer rowIdForDeletion = Integer.valueOf(
                rowForDeletion.getTableRowPkId().getRowId());
        DbRow.DeleteRowDto deleteRowDto = new DbRow.DeleteRowDto(tableIdForDeletion,
                rowIdForDeletion);

        given().contentType(ContentType.JSON)
                .body(deleteRowDto)
                .when()
                .delete("/row")
                .then()
                .statusCode(200);

        List<TableRowEntity> updatedRowsForBlock = tableRowRepository.getForTableSortByPos(block.getBlockId());

        for (int i = 0; i < updatedRowsForBlock.size(); i++) {
            if (i < rowForDeletion.getPos()) {
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getPos(), updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getParentText(),
                        updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getCellsText(),
                        updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getBbox(), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getScore(), updatedRowsForBlock.get(i).getScore());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getNtaTable(),
                        updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getComment(),
                        updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getLayoutBlock().getBlockId(),
                        updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getTableRowPkId(),
                        updatedRowsForBlock.get(i).getTableRowPkId());
            } else {

                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getPos() - 1, updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getParentText(),
                        updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getCellsText(),
                        updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getBbox(), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getScore(), updatedRowsForBlock.get(i).getScore());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getNtaTable(),
                        updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getComment(),
                        updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getLayoutBlock().getBlockId(),
                        updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getTableRowPkId(),
                        updatedRowsForBlock.get(i).getTableRowPkId());
            }
        }
    }

    @Test
    @TestSecurity(user = "user")
    void testDeleteRowWithCoaMapping() {
        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        LayoutBlockEntity block = blocks.get(1);

        List<TableRowEntity> rowsBeforeUpdate = block.getTableRows()
                .stream()
                .sorted(Comparator.comparingInt(TableRowEntity::getPos))
                .toList();

        TableRowEntity rowForDeletion = block.getTableRows().get(1);
        Integer tableIdForDeletion = rowForDeletion.getTableRowPkId().getTableId();
        Integer rowIdForDeletion = Integer.valueOf(
                rowForDeletion.getTableRowPkId().getRowId());
        addCoaDataMapping(rowForDeletion);

        DbRow.DeleteRowDto deleteRowDto = new DbRow.DeleteRowDto(tableIdForDeletion,
                rowIdForDeletion);

        given().contentType(ContentType.JSON)
                .body(deleteRowDto)
                .when()
                .delete("/row")
                .then()
                .statusCode(200);

        List<TableRowEntity> updatedRowsForBlock = tableRowRepository.getForTableSortByPos(block.getBlockId());

        for (int i = 0; i < updatedRowsForBlock.size(); i++) {
            if (i < rowForDeletion.getPos()) {
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getPos(), updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getParentText(),
                        updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getCellsText(),
                        updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getBbox(), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getScore(), updatedRowsForBlock.get(i).getScore());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getNtaTable(),
                        updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getComment(),
                        updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getLayoutBlock().getBlockId(),
                        updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i).getTableRowPkId(),
                        updatedRowsForBlock.get(i).getTableRowPkId());
            } else {

                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getPos() - 1, updatedRowsForBlock.get(i).getPos());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getParentText(),
                        updatedRowsForBlock.get(i).getParentText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getCellsText(),
                        updatedRowsForBlock.get(i).getCellsText());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getBbox(), updatedRowsForBlock.get(i).getBbox());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getScore(), updatedRowsForBlock.get(i).getScore());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getNtaTable(),
                        updatedRowsForBlock.get(i).getNtaTable());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getComment(),
                        updatedRowsForBlock.get(i).getComment());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getLayoutBlock().getBlockId(),
                        updatedRowsForBlock.get(i).getLayoutBlock().getBlockId());
                Assertions.assertEquals(rowsBeforeUpdate.get(i + 1).getTableRowPkId(),
                        updatedRowsForBlock.get(i).getTableRowPkId());
            }
        }
    }

    @Transactional
    public void addCoaDataMapping(TableRowEntity rowForDeletion) {
        CoaDataEntity coaData = CoaDataEntity.builder().coaId(1).coaScore((byte) 100).useCoa(false).build();
        coaDataRepository.persist(coaData);

        extractedRowCoaDataRepository.saveRowCoaJoin(
                List.of(new ExtractedTableRowCoaDataJoinEntity(rowForDeletion.getTableRowPkId(), coaData.getId(),
                        null)));
    }
}
