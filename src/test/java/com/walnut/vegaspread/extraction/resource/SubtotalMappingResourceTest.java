package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.entity.SubtotalMappingEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.SubtotalMappingDto;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.SubtotalMappingRepository;
import com.walnut.vegaspread.extraction.repository.SubtotalRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static com.walnut.vegaspread.extraction.service.SubtotalMappingService.REMOVE_SUBTOTAL_ID;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(SubtotalMappingResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SubtotalMappingResourceTest {

    @Inject
    SubtotalMappingRepository subtotalMappingRepository;
    @Inject
    SubtotalRepository subtotalRepository;
    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    List<SubtotalMappingEntity> createSubtotalMappings(UUID docId) {
        createBlocks(docId);
        addSubtotals("walnut", "rcbc");

        List<SubtotalMappingEntity> subtotalMappingEntities = IntStream.range(1, 3).mapToObj(i -> {
            SubtotalMappingEntity subtotalMappingEntity = new SubtotalMappingEntity();
            subtotalMappingEntity.setSubtotal(subtotalRepository.findById(i));
            subtotalMappingEntity.setId(new TableRowPkId(i, i));
            subtotalMappingEntity.setDocId(docId);
            return subtotalMappingEntity;
        }).toList();
        subtotalMappingRepository.persist(subtotalMappingEntities);
        return subtotalMappingEntities;
    }

    @Transactional
    void addSubtotals(String coaClient, String client) {

        List<SubtotalEntity> subtotalEntities = IntStream.range(0, 10).mapToObj(i -> {
            SubtotalEntity subtotalEntity = new SubtotalEntity();
            subtotalEntity.setClient(client);
            subtotalEntity.setCoaClient(coaClient);
            subtotalEntity.setSubtotalName("subtotal" + i);
            subtotalEntity.setExcelRowNumber((short) i);
            return subtotalEntity;
        }).toList();
        subtotalRepository.persist(subtotalEntities);
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId) {
        List<LayoutBlockEntity> blocks = IntStream.range(0, 5).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag("tag" + id)
                .blockType(BlockTypeEnum.TABLE)
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        List<TableHeaderEntity> headers = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableHeaderEntity> blockHeaders = IntStream.range(0, 3)
                    .mapToObj(id -> TableHeaderEntity.builder()
                            .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .text("header" + id)
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .pos(id)
                            .build())
                    .toList();
            headers.addAll(blockHeaders);
            block.setTableHeaders(blockHeaders);
        }
        tableHeaderRepository.persist(headers);

        List<TableRowEntity> rows = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableRowEntity> blockRows = IntStream.range(0, 3)
                    .mapToObj(id -> TableRowEntity.builder()
                            .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .cellsText(List.of("row" + id, "row" + id))
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .comment("comment" + id)
                            .parentText("parent" + id)
                            .pos(id)
                            .build())
                    .toList();
            rows.addAll(blockRows);
            block.setTableRows(blockRows);
        }
        tableRowRepository.persist(rows);
        return blocks;
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testCreateSubtotalMappings() {
        UUID docId = UUID.randomUUID();
        String coaClientName = "walnut";
        String clientName = "rcbc";
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        addSubtotals(coaClientName, clientName);

        List<SubtotalMappingDto.CreateOrUpdateMapping> subtotalMappingCreateDtos = IntStream.range(1, 3)
                .mapToObj(id -> new SubtotalMappingDto.CreateOrUpdateMapping(
                        blocks.get(id).getTableRows().get(id).getTableRowPkId().getTableId(),
                        blocks.get(id).getTableRows().get(id).getTableRowPkId().getRowId().intValue(), id)).toList();
        given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .body(subtotalMappingCreateDtos)
                .when()
                .post("/{docId}")
                .then().statusCode(200);

        for (SubtotalMappingDto.CreateOrUpdateMapping dto : subtotalMappingCreateDtos) {
            SubtotalMappingEntity subtotalMappingEntity = subtotalMappingRepository.findById(
                    new TableRowPkId(dto.tableId(), dto.rowId()));

            Assertions.assertEquals(dto.subtotalId(), subtotalMappingEntity.getSubtotal().getId());
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testUpdateSubtotalMappings() {
        UUID docId = UUID.randomUUID();

        List<SubtotalMappingEntity> subtotalMappingEntities = createSubtotalMappings(docId);
        SubtotalMappingEntity subtotalMappingEntityForUpdate = subtotalMappingEntities.get(0);

        List<SubtotalMappingDto.CreateOrUpdateMapping> subtotalMappingUpdateDtos = List.of(
                new SubtotalMappingDto.CreateOrUpdateMapping(subtotalMappingEntityForUpdate.getId().getTableId(),
                        subtotalMappingEntityForUpdate.getId().getRowId().intValue(),
                        subtotalMappingEntityForUpdate.getSubtotal()
                                .getId() + 1));

        given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .body(subtotalMappingUpdateDtos)
                .when()
                .post("/{docId}")
                .then().statusCode(200);

        for (SubtotalMappingDto.CreateOrUpdateMapping dto : subtotalMappingUpdateDtos) {
            SubtotalMappingEntity subtotalMappingEntity = subtotalMappingRepository.findById(
                    new TableRowPkId(dto.tableId(), dto.rowId()));
            Assertions.assertEquals(dto.subtotalId(), subtotalMappingEntity.getSubtotal().getId());
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testDeleteSubtotalMappings() {
        UUID docId = UUID.randomUUID();

        List<SubtotalMappingEntity> subtotalMappingEntities = createSubtotalMappings(docId);
        SubtotalMappingEntity subtotalMappingEntityForUpdate = subtotalMappingEntities.get(0);

        List<SubtotalMappingDto.CreateOrUpdateMapping> subtotalMappingUpdateDtos = List.of(
                new SubtotalMappingDto.CreateOrUpdateMapping(subtotalMappingEntityForUpdate.getId().getTableId(),
                        subtotalMappingEntityForUpdate.getId().getRowId().intValue(), REMOVE_SUBTOTAL_ID));

        given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .body(subtotalMappingUpdateDtos)
                .when()
                .post("/{docId}")
                .then().statusCode(200);

        for (SubtotalMappingDto.CreateOrUpdateMapping dto : subtotalMappingUpdateDtos) {
            SubtotalMappingEntity subtotalMappingEntity = subtotalMappingRepository.findById(
                    new TableRowPkId(dto.tableId(), dto.rowId()));
            Assertions.assertNull(subtotalMappingEntity);
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testGetSubtotalMappings() {
        UUID docId = UUID.randomUUID();
        int tableId = 1;

        List<SubtotalMappingEntity> subtotalMappingEntities = createSubtotalMappings(docId);
        List<SubtotalMappingEntity> subtotalMappingForTable = subtotalMappingEntities.stream()
                .filter(subtotalMappingEntity -> subtotalMappingEntity.getId().getTableId() == tableId)
                .toList();

        List<SubtotalMappingDto.Response> response = given().contentType(ContentType.JSON)
                .queryParam("tableId", tableId)
                .when()
                .get()
                .then().statusCode(200).extract().body().as(new TypeRef<>() {
                });

        for (int i = 0; i < subtotalMappingForTable.size(); i++) {
            SubtotalMappingDto.Response responseDto = response.get(i);
            SubtotalMappingEntity subtotalMappingEntity = subtotalMappingForTable.get(i);
            Assertions.assertEquals(subtotalMappingEntity.getId().getTableId(), responseDto.tableId());
            Assertions.assertEquals(subtotalMappingEntity.getId().getRowId(), responseDto.rowId());
            Assertions.assertEquals(subtotalMappingEntity.getSubtotal().getId(), responseDto.subtotalId());
            Assertions.assertEquals(subtotalMappingEntity.getDocId(), responseDto.docId());
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testGetSubtotalMappingsForDoc() {
        UUID docId = UUID.randomUUID();

        List<SubtotalMappingEntity> subtotalMappingEntities = createSubtotalMappings(docId);

        List<SubtotalMappingDto.Response> response = given().contentType(ContentType.JSON)
                .pathParam("docId", docId)
                .when()
                .get("/{docId}")
                .then().statusCode(200).extract().body().as(new TypeRef<>() {
                });

        for (int i = 0; i < subtotalMappingEntities.size(); i++) {
            SubtotalMappingDto.Response responseDto = response.get(i);
            SubtotalMappingEntity subtotalMappingEntity = subtotalMappingEntities.get(i);
            Assertions.assertEquals(subtotalMappingEntity.getId().getTableId(), responseDto.tableId());
            Assertions.assertEquals(subtotalMappingEntity.getId().getRowId(), responseDto.rowId());
            Assertions.assertEquals(subtotalMappingEntity.getSubtotal().getId(), responseDto.subtotalId());
            Assertions.assertEquals(subtotalMappingEntity.getDocId(), responseDto.docId());
        }
    }
}

