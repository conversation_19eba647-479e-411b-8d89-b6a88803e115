package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.CoaDataDto;
import com.walnut.vegaspread.extraction.model.ExtractedTableRowCoaDataJoinDto;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.apache.commons.lang3.StringUtils;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ExtractedRowCoaDataJoinResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ExtractedRowCoaDataResourceTest {

    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    CoaMappingRepository coaMappingRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId) {
        List<LayoutBlockEntity> blocks = IntStream.range(0, 5).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag("tag" + id)
                .blockType(BlockTypeEnum.TABLE)
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        List<TableHeaderEntity> headers = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableHeaderEntity> blockHeaders = IntStream.range(0, 3)
                    .mapToObj(id -> TableHeaderEntity.builder()
                            .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .text("header" + id)
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .pos(id)
                            .build())
                    .toList();
            headers.addAll(blockHeaders);
            block.setTableHeaders(blockHeaders);
        }
        tableHeaderRepository.persist(headers);

        List<TableRowEntity> rows = new ArrayList<>();
        for (LayoutBlockEntity block : blocks) {
            List<TableRowEntity> blockRows = IntStream.range(0, 3)
                    .mapToObj(id -> TableRowEntity.builder()
                            .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                            .layoutBlock(block)
                            .cellsText(List.of("row" + id, "row" + id))
                            .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                            .score((byte) new Random().nextInt(100))
                            .comment("comment" + id)
                            .parentText("parent" + id)
                            .pos(id)
                            .build())
                    .toList();
            rows.addAll(blockRows);
            block.setTableRows(blockRows);
        }
        tableRowRepository.persist(rows);
        return blocks;
    }

    @Transactional
    public CoaDataEntity addCoaData(Integer coaId, Integer coaScore, Boolean useCoa) {
        CoaDataEntity coaData = CoaDataEntity.builder()
                .coaId(coaId)
                .coaScore(coaScore.byteValue())
                .useCoa(useCoa)
                .build();
        coaDataRepository.persist(coaData);
        return coaData;
    }

    @Transactional
    public void addCoaDataMapping(TableRowEntity row, Integer coaDataId, CoaMappingEntity explainability) {

        extractedRowCoaDataRepository.saveRowCoaJoin(
                List.of(new ExtractedTableRowCoaDataJoinEntity(row.getTableRowPkId(), coaDataId,
                        explainability)));
    }

    @Transactional
    public CoaMappingEntity addCoaMapping() {
        CoaMappingEntity coaMappingEntity = CoaMappingEntity.builder()
                .tableId(100814)
                .rowId((short) 7)
                .docId(UUID.randomUUID())
                .tableType("Balance Sheet")
                .rowParent(StringUtils.EMPTY)
                .text("(b)")
                .fsHeader(StringUtils.EMPTY)
                .fsText("(b)")
                .coaId(5)
                .isApproved(true)
                .build();
        coaMappingRepository.persist(coaMappingEntity);
        return coaMappingEntity;
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    void testCreateCoaDataMapping() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity coaData = addCoaData(2, 50, false);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(coaData.getCoaId(), coaData.getCoaScore().intValue(),
                                coaData.getUseCoa()), null));
        given().contentType(ContentType.JSON)
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post()
                .then().statusCode(200);

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : extractedTableRowCoaDataJoinDtos) {
            Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                    extractedRowCoaDataRepository.findByRowOptional(dto.tableId(), dto.rowId().shortValue());
            assert optExtractedTableRowCoaDataJoinEntity.isPresent();
            Assertions.assertEquals(coaData.getId(),
                    optExtractedTableRowCoaDataJoinEntity.get().getExtractedTableRowCoaDataPkId().getCoaDataId());
            Assertions.assertNull(optExtractedTableRowCoaDataJoinEntity.get().getExplainability());
        }
    }

    @Test
    @TestSecurity(user = "user", roles = Roles.MAP_COA)
    void testUpdateCoaDataMapping() {

        UUID docId = UUID.randomUUID();
        List<LayoutBlockEntity> blocks = createBlocks(docId);
        TableRowEntity tableRow = blocks.get(0).getTableRows().get(0);
        CoaDataEntity currentCoaData = addCoaData(2, 50, false);
        CoaMappingEntity currentExplainability = addCoaMapping();
        addCoaDataMapping(tableRow, currentCoaData.getId(), currentExplainability);

        CoaDataEntity newCoaData = addCoaData(3, 12, true);

        List<ExtractedTableRowCoaDataJoinDto.CreateOrUpdate> extractedTableRowCoaDataJoinDtos = List.of(
                new ExtractedTableRowCoaDataJoinDto.CreateOrUpdate(tableRow.getTableRowPkId().getTableId(),
                        tableRow.getTableRowPkId().getRowId().intValue(),
                        new CoaDataDto.Create(newCoaData.getCoaId(), newCoaData.getCoaScore().intValue(),
                                newCoaData.getUseCoa()), null));
        given().contentType(ContentType.JSON)
                .body(extractedTableRowCoaDataJoinDtos)
                .when()
                .post()
                .then().statusCode(200);

        for (ExtractedTableRowCoaDataJoinDto.CreateOrUpdate dto : extractedTableRowCoaDataJoinDtos) {
            Optional<ExtractedTableRowCoaDataJoinEntity> optExtractedTableRowCoaDataJoinEntity =
                    extractedRowCoaDataRepository.findByRowOptional(dto.tableId(), dto.rowId().shortValue());
            assert optExtractedTableRowCoaDataJoinEntity.isPresent();
            Assertions.assertEquals(newCoaData.getId(),
                    optExtractedTableRowCoaDataJoinEntity.get().getExtractedTableRowCoaDataPkId().getCoaDataId());
            Assertions.assertNull(optExtractedTableRowCoaDataJoinEntity.get().getExplainability());
        }
    }
}
