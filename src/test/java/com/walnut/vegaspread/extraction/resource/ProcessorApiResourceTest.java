package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.clients.IamApiClient;
import com.walnut.vegaspread.extraction.entity.Bbox;
import com.walnut.vegaspread.extraction.entity.CoaDataEntity;
import com.walnut.vegaspread.extraction.entity.CoaMappingEntity;
import com.walnut.vegaspread.extraction.entity.ExtractedTableRowCoaDataJoinEntity;
import com.walnut.vegaspread.extraction.entity.LayoutBlockEntity;
import com.walnut.vegaspread.extraction.entity.TableHeaderEntity;
import com.walnut.vegaspread.extraction.entity.TableRowEntity;
import com.walnut.vegaspread.extraction.model.BlockTypeEnum;
import com.walnut.vegaspread.extraction.model.DbBlock;
import com.walnut.vegaspread.extraction.model.DbHeader;
import com.walnut.vegaspread.extraction.model.DbRow;
import com.walnut.vegaspread.extraction.model.MappedRowDto;
import com.walnut.vegaspread.extraction.model.ResponseDto;
import com.walnut.vegaspread.extraction.primarykey.TableHeaderPkId;
import com.walnut.vegaspread.extraction.primarykey.TableRowPkId;
import com.walnut.vegaspread.extraction.repository.CoaDataRepository;
import com.walnut.vegaspread.extraction.repository.CoaMappingRepository;
import com.walnut.vegaspread.extraction.repository.ExtractedRowCoaDataRepository;
import com.walnut.vegaspread.extraction.repository.LayoutBlockRepository;
import com.walnut.vegaspread.extraction.repository.TableHeaderRepository;
import com.walnut.vegaspread.extraction.repository.TableRowRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import com.walnut.vegaspread.extraction.service.mapper.LayoutBlockEntityMapper;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.eclipse.microprofile.rest.client.inject.RestClient;
import org.flywaydb.core.Flyway;
import org.jboss.resteasy.reactive.RestResponse;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(ProcessorApiResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class ProcessorApiResourceTest {
    private static final String LAYOUT_PREFIX = "/block";
    private static final String TABLE_PREFIX = "/table/extracted";

    @InjectMock
    @RestClient
    IamApiClient iamApiClient;
    @Inject
    LayoutBlockRepository layoutBlockRepository;
    @Inject
    TableRowRepository tableRowRepository;
    @Inject
    TableHeaderRepository tableHeaderRepository;
    @Inject
    ExtractedRowCoaDataRepository extractedRowCoaDataRepository;
    @Inject
    CoaDataRepository coaDataRepository;
    @Inject
    LayoutBlockEntityMapper layoutBlockEntityMapper;
    @Inject
    CoaMappingRepository coaMappingRepository;
    @InjectMock
    ExchangeService exchangeService;
    @Inject
    Flyway flyway;
    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @BeforeEach
    void setUp() {
        Mockito.when(iamApiClient.validateApiKey(Mockito.any(), Mockito.any())).thenReturn(RestResponse.ok());
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    List<LayoutBlockEntity> createBlocks(UUID docId, int numBlocks, boolean createHeaders, boolean createRows) {
        List<LayoutBlockEntity> blocks = IntStream.range(0, numBlocks).mapToObj(id -> LayoutBlockEntity.builder()
                .docId(docId)
                .pageNum((short) (id % 3))
                .tag("tag" + id)
                .blockType(createRows || createHeaders ? BlockTypeEnum.TABLE :
                        BlockTypeEnum.values()[id % BlockTypeEnum.values().length])
                .score((byte) new Random().nextInt(100))
                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                .tagExplainabilityId(id)
                .build()).toList();
        layoutBlockRepository.persist(blocks);

        if (createHeaders) {
            List<TableHeaderEntity> headers = new ArrayList<>();
            for (LayoutBlockEntity block : blocks) {
                List<TableHeaderEntity> blockHeaders = IntStream.range(0, 3)
                        .mapToObj(id -> TableHeaderEntity.builder()
                                .tableHeaderPkId(new TableHeaderPkId(block.getBlockId(), id))
                                .layoutBlock(block)
                                .text("header" + id)
                                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                                .score((byte) new Random().nextInt(100))
                                .pos(id)
                                .build())
                        .toList();
                headers.addAll(blockHeaders);
                block.setTableHeaders(blockHeaders);
            }
            tableHeaderRepository.persist(headers);
        }

        if (createRows) {
            List<TableRowEntity> rows = new ArrayList<>();
            for (LayoutBlockEntity block : blocks) {
                List<TableRowEntity> blockRows = IntStream.range(0, 3)
                        .mapToObj(id -> TableRowEntity.builder()
                                .tableRowPkId(new TableRowPkId(block.getBlockId(), id))
                                .layoutBlock(block)
                                .cellsText(List.of("row" + id, "row" + id))
                                .bbox(new Bbox(getCoord(), getCoord(), getCoord(), getCoord()))
                                .score((byte) new Random().nextInt(100))
                                .comment("comment" + id)
                                .parentText("parent" + id)
                                .pos(id)
                                .build())
                        .toList();
                rows.addAll(blockRows);
                block.setTableRows(blockRows);
            }
            tableRowRepository.persist(rows);
        }
        return blocks;
    }

    @Transactional
    void linkNtaTable(TableRowPkId rowPkId, LayoutBlockEntity ntaTable) {
        TableRowEntity row = tableRowRepository.findById(rowPkId);
        row.setNtaTable(layoutBlockRepository.findById(ntaTable.getBlockId()));
        tableRowRepository.persist(row);
    }

    @Transactional
    TableRowEntity assignCoaId(TableRowPkId rowPkId, Integer coaId, Boolean useCoa) {
        TableRowEntity row = tableRowRepository.findById(rowPkId);

        CoaDataEntity coaDataEntity = new CoaDataEntity();
        coaDataEntity.setCoaId(coaId);
        coaDataEntity.setCoaScore((byte) 100);
        coaDataEntity.setUseCoa(useCoa);
        coaDataRepository.persist(coaDataEntity);

        ExtractedTableRowCoaDataJoinEntity extractedTableRowCoaDataJoinEntity = new ExtractedTableRowCoaDataJoinEntity(
                row.getTableRowPkId(), coaDataEntity.getId(), null);
        extractedRowCoaDataRepository.persist(extractedTableRowCoaDataJoinEntity);

        return row;
    }

    int getCoord() {
        return new Random().nextInt(1000);
    }

    @Test
    void testCreateBlock() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        Random rng = new Random();
        List<DbBlock.CreateBlockDto> createBlockDtos = IntStream.range(0, numBlocks)
                .mapToObj(i -> new DbBlock.CreateBlockDto(rng.nextInt(10), BlockTypeEnum.TABLE, getCoord(),
                        getCoord(), getCoord(), getCoord(), getCoord(), "tag" + i,
                        "comment" + i))
                .toList();

        given().contentType(ContentType.JSON)
                .body(createBlockDtos)
                .when()
                .post(LAYOUT_PREFIX + "/{docId}", docId)
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByDocIdAndBlockType(docId, BlockTypeEnum.TABLE);
        Assertions.assertEquals(numBlocks, dbBlocks.size());

        for (int i = 0; i < numBlocks; i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            DbBlock.CreateBlockDto createBlockDto = createBlockDtos.get(i);
            Assertions.assertEquals(docId, dbBlock.getDocId());
            Assertions.assertEquals(createBlockDto.pageNum(), (int) dbBlock.getPageNum());
            Assertions.assertEquals(createBlockDto.blockType(), dbBlock.getBlockType());
            Assertions.assertEquals(createBlockDto.xMin(), (int) dbBlock.getBbox().getXMin());
            Assertions.assertEquals(createBlockDto.xMax(), (int) dbBlock.getBbox().getXMax());
            Assertions.assertEquals(createBlockDto.yMin(), (int) dbBlock.getBbox().getYMin());
            Assertions.assertEquals(createBlockDto.yMax(), (int) dbBlock.getBbox().getYMax());
            Assertions.assertEquals(createBlockDto.tag(), dbBlock.getTag());
            Assertions.assertEquals(createBlockDto.comment(), dbBlock.getComment());
        }
    }

    @Test
    void testDeleteBlockByBlockId() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, false);
        int blockId = blocks.get(0).getBlockId();

        given().when().delete(LAYOUT_PREFIX + "/{blockId}", blockId).then().statusCode(204);

        LayoutBlockEntity dbBlock = layoutBlockRepository.findById(blockId);
        Assertions.assertNull(dbBlock);
    }

    @Test
    void testDeleteBlockBatch() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, false);
        List<Integer> blockIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        long deltedBlocks = given().contentType(ContentType.JSON)
                .body(blockIds)
                .when()
                .delete(LAYOUT_PREFIX + "/batch")
                .then()
                .statusCode(200)
                .extract().body().as(Long.class);

        Assertions.assertEquals(blockIds.size(), deltedBlocks);
        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(blockIds);
        Assertions.assertEquals(0, dbBlocks.size());
    }

    @Test
    void testDeleteBlocksByDocId() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        createBlocks(docId, numBlocks, false, false);

        long deleteCount = given().when()
                .delete(LAYOUT_PREFIX + "/doc/{docId}", docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(Long.class);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.listAll();
        Assertions.assertEquals(numBlocks, deleteCount);
        Assertions.assertEquals(0, dbBlocks.size());
    }

    @Test
    void testUpdateBlocks() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, false);
        List<DbBlock.UpdateBlockTagByProcessorDto> updateBlockTagDtos = blocks.stream()
                .map(block -> new DbBlock.UpdateBlockTagByProcessorDto(block.getBlockId(),
                        "newTag" + block.getBlockId(), block.getBlockId() % 2 == 0 ? null : 5))
                .toList();

        given().contentType(ContentType.JSON)
                .body(updateBlockTagDtos)
                .queryParams("spreadId", 0)
                .when()
                .patch(LAYOUT_PREFIX + "/tags")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());
        for (int i = 0; i < numBlocks; i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            DbBlock.UpdateBlockTagByProcessorDto updateBlockTagDto = updateBlockTagDtos.get(i);
            Assertions.assertEquals(updateBlockTagDto.blockId(), dbBlock.getBlockId());
            Assertions.assertEquals(updateBlockTagDto.tag(), dbBlock.getTag());
            Assertions.assertEquals(Optional.ofNullable(updateBlockTagDto.tagExplainabilityId())
                    .orElse(blocks.get(i).getTagExplainabilityId()), dbBlock.getTagExplainabilityId());
        }
    }

    @Test
    void testGetTableTags() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 10;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, false);
        List<LayoutBlockEntity> tables = blocks.stream()
                .filter(block -> block.getBlockType() == BlockTypeEnum.TABLE)
                .toList();

        DbBlock.BlockTagOnly[] tags = given().when()
                .get(LAYOUT_PREFIX + "/doc/{docId}/tags", docId)
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(DbBlock.BlockTagOnly[].class);

        Assertions.assertEquals(tables.size(), tags.length);
        for (int i = 0; i < tags.length; i++) {
            DbBlock.BlockTagOnly tag = tags[i];
            LayoutBlockEntity dbBlock = tables.get(i);
            Assertions.assertEquals(dbBlock.getBlockId(), tag.getBlockId());
            Assertions.assertEquals(dbBlock.getTag(), tag.getTag());
        }
    }

    @Test
    void testCreateTableHeaders() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 3;
        Random rng = new Random();

        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, false);
        List<DbHeader.InsertHeaderDtoForProcessor> insertHeaderDtos = blocks.stream()
                .map(block -> new DbHeader.InsertHeaderDtoForProcessor(block.getBlockId(), block.getBlockId(),
                        "header" + block.getBlockId(), getCoord(), getCoord(), getCoord(), getCoord(),
                        rng.nextInt(100)))
                .toList();

        given().contentType(ContentType.JSON)
                .body(insertHeaderDtos)
                .queryParam("deleteExisting", true)
                .when()
                .post(TABLE_PREFIX + "/headers")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (int i = 0; i < numBlocks; i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            List<TableHeaderEntity> dbHeaders = dbBlock.getTableHeaders();
            List<DbHeader.InsertHeaderDtoForProcessor> headerDtos = insertHeaderDtos.stream()
                    .filter(headerDto -> headerDto.tableId() == dbBlock.getBlockId())
                    .toList();

            Assertions.assertEquals(headerDtos.size(), dbHeaders.size());
            for (int j = 0; j < headerDtos.size(); j++) {
                DbHeader.InsertHeaderDtoForProcessor headerDto = headerDtos.get(j);
                TableHeaderEntity dbHeader = dbHeaders.get(j);
                Assertions.assertEquals(headerDto.tableId(), dbHeader.getTableHeaderPkId().getTableId());
                Assertions.assertEquals(headerDto.tableId(), dbHeader.getPos());
                Assertions.assertEquals(headerDto.text(), dbHeader.getText());
                Assertions.assertEquals(headerDto.xMin(), (int) dbHeader.getBbox().getXMin());
                Assertions.assertEquals(headerDto.xMax(), (int) dbHeader.getBbox().getXMax());
                Assertions.assertEquals(headerDto.yMin(), (int) dbHeader.getBbox().getYMin());
                Assertions.assertEquals(headerDto.yMax(), (int) dbHeader.getBbox().getYMax());
                Assertions.assertEquals(headerDto.score(), (int) dbHeader.getScore());
            }
        }
    }

    @Test
    void testCreateTableRows() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 3;
        Random rng = new Random();

        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, false);
        List<DbRow.InsertRowDtoForProcessor> insertRowDtos = blocks.stream()
                .map(block -> new DbRow.InsertRowDtoForProcessor(block.getBlockId(), block.getBlockId(),
                        List.of("row" + block.getBlockId(), "row" + block.getBlockId()), getCoord(), getCoord(),
                        getCoord(), getCoord(), rng.nextInt(100), "comment" + block.getBlockId(),
                        "parent" + block.getBlockId()))
                .toList();

        given().contentType(ContentType.JSON)
                .queryParam("deleteExisting", true)
                .body(insertRowDtos)
                .when()
                .post(TABLE_PREFIX + "/rows")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());

        for (int i = 0; i < numBlocks; i++) {
            LayoutBlockEntity dbBlock = dbBlocks.get(i);
            List<TableRowEntity> dbRows = dbBlock.getTableRows();
            List<DbRow.InsertRowDtoForProcessor> rowDtos = insertRowDtos.stream()
                    .filter(rowDto -> rowDto.tableId() == dbBlock.getBlockId())
                    .toList();

            Assertions.assertEquals(rowDtos.size(), dbRows.size());
            for (int j = 0; j < rowDtos.size(); j++) {
                DbRow.InsertRowDtoForProcessor rowDto = rowDtos.get(j);
                TableRowEntity dbRow = dbRows.get(j);
                Assertions.assertEquals(rowDto.tableId(), dbRow.getTableRowPkId().getTableId());
                Assertions.assertEquals(rowDto.rowId(), (int) dbRow.getTableRowPkId().getRowId());
                Assertions.assertEquals(rowDto.cellsText(), dbRow.getCellsText());
                Assertions.assertEquals(rowDto.xMin(), (int) dbRow.getBbox().getXMin());
                Assertions.assertEquals(rowDto.xMax(), (int) dbRow.getBbox().getXMax());
                Assertions.assertEquals(rowDto.yMin(), (int) dbRow.getBbox().getYMin());
                Assertions.assertEquals(rowDto.yMax(), (int) dbRow.getBbox().getYMax());
                Assertions.assertEquals(rowDto.score(), (int) dbRow.getScore());
                Assertions.assertEquals(rowDto.comment(), dbRow.getComment());
                Assertions.assertEquals(rowDto.parentText(), dbRow.getParentText());
            }
        }
    }

    @Test
    void testDeleteTableHeaders() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 3;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, true, false);
        List<Integer> tableIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        long deleteCount = given().contentType(ContentType.JSON)
                .body(tableIds)
                .when()
                .delete(TABLE_PREFIX + "/headers")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(Long.class);

        Assertions.assertEquals(numBlocks * 3, deleteCount);
        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());
        for (LayoutBlockEntity dbBlock : dbBlocks) {
            Assertions.assertEquals(0, dbBlock.getTableHeaders().size());
        }
    }

    @Test
    void testDeleteTableRows() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 3;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, true);
        List<Integer> tableIds = blocks.stream().map(LayoutBlockEntity::getBlockId).toList();

        long deletedBlocks = given().contentType(ContentType.JSON)
                .body(tableIds)
                .when()
                .delete(TABLE_PREFIX + "/rows")
                .then()
                .statusCode(200)
                .extract().body().as(Long.class);

        Assertions.assertEquals(tableIds.size() * 3L, deletedBlocks);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                blocks.stream().map(LayoutBlockEntity::getBlockId).toList());
        for (LayoutBlockEntity dbBlock : dbBlocks) {
            Assertions.assertEquals(0, dbBlock.getTableRows().size());
        }
    }

    @Test
    void testUpdateTableRows() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 3;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, true, true);
        List<DbRow.RowDto> updateRowDtos = blocks.stream().skip(1)
                .flatMap(block -> block.getTableRows().stream())
                .map(row -> DbRow.RowDto.builder()
                        .tableId(row.getTableRowPkId().getTableId())
                        .rowId((int) row.getTableRowPkId().getRowId())
                        .cellsText(List.of("newRow" + row.getTableRowPkId().getRowId(),
                                "newRow" + row.getTableRowPkId().getRowId(),
                                "newRow" + row.getTableRowPkId().getRowId()))
                        .xMin(getCoord())
                        .xMax(getCoord())
                        .yMin(getCoord())
                        .yMax(getCoord())
                        .score(new Random().nextInt(100))
                        .comment("newComment" + row.getTableRowPkId().getRowId())
                        .parentText("newParent" + row.getTableRowPkId().getRowId())
                        .useCoa(new Random().nextBoolean())
                        .coaId(new Random().nextInt(2, 100))
                        .coaScore(new Random().nextInt(100))
                        .headerIds(List.of(1, 2, 3))
                        .ntaTableId(1)
                        .build())
                .toList();

        given().contentType(ContentType.JSON)
                .body(updateRowDtos)
                .param("spreadId", 0)
                .when()
                .patch(TABLE_PREFIX + "/rows")
                .then()
                .statusCode(200);

        List<LayoutBlockEntity> dbBlocks = layoutBlockRepository.findAllByIds(
                updateRowDtos.stream().map(DbRow.RowDto::getTableId).distinct().toList());
        for (LayoutBlockEntity dbBlock : dbBlocks) {
            List<TableRowEntity> dbRows = dbBlock.getTableRows();
            List<DbRow.RowDto> rowDtos = updateRowDtos.stream()
                    .filter(rowDto -> rowDto.getTableId() == dbBlock.getBlockId())
                    .toList();

            Assertions.assertEquals(rowDtos.size(), dbRows.size());
            for (int j = 0; j < rowDtos.size(); j++) {
                DbRow.RowDto rowDto = rowDtos.get(j);
                TableRowEntity dbRow = dbRows.get(j);
                ExtractedTableRowCoaDataJoinEntity extractedTableRowCoaDataJoinEntity =
                        extractedRowCoaDataRepository.findByRowOptional(
                                dbRow.getTableRowPkId().getTableId(), dbRow.getTableRowPkId().getRowId()).get();
                CoaDataEntity coaDataEntity = coaDataRepository.findById(
                        extractedTableRowCoaDataJoinEntity.getExtractedTableRowCoaDataPkId().getCoaDataId());
                Assertions.assertEquals(rowDto.getTableId(), dbRow.getTableRowPkId().getTableId());
                Assertions.assertEquals(rowDto.getRowId(), (int) dbRow.getTableRowPkId().getRowId());
                Assertions.assertEquals(rowDto.getCellsText(), dbRow.getCellsText());
                Assertions.assertEquals(rowDto.getXMin(), (int) dbRow.getBbox().getXMin());
                Assertions.assertEquals(rowDto.getXMax(), (int) dbRow.getBbox().getXMax());
                Assertions.assertEquals(rowDto.getYMin(), (int) dbRow.getBbox().getYMin());
                Assertions.assertEquals(rowDto.getYMax(), (int) dbRow.getBbox().getYMax());
                Assertions.assertEquals(rowDto.getScore(), (int) dbRow.getScore());
                Assertions.assertEquals(rowDto.getComment(), dbRow.getComment());
                Assertions.assertEquals(rowDto.getParentText(), dbRow.getParentText());
                Assertions.assertEquals(rowDto.getUseCoa(), coaDataEntity.getUseCoa());
                Assertions.assertEquals(rowDto.getCoaId(), coaDataEntity.getCoaId());
                Assertions.assertEquals(rowDto.getCoaScore().byteValue(), coaDataEntity.getCoaScore());
                Assertions.assertEquals(rowDto.getExplainabilityId(),
                        extractedTableRowCoaDataJoinEntity.getExplainability() == null ? null :
                                extractedTableRowCoaDataJoinEntity.getExplainability()
                                        .getId());
                Assertions.assertEquals(rowDto.getHeaderIds(), dbRow.getHeaderIds());
            }
        }
    }

    @Test
    void testGetMappedCoaRows() {
        UUID docId = UUID.randomUUID();
        int numBlocks = 3;
        List<LayoutBlockEntity> blocks = createBlocks(docId, numBlocks, false, true);
        TableRowEntity fsRow = blocks.get(0).getTableRows().get(0);
        LayoutBlockEntity ntaTable = blocks.get(1);
        linkNtaTable(fsRow.getTableRowPkId(), ntaTable);
        TableRowEntity ntaRow = assignCoaId(ntaTable.getTableRows().get(0).getTableRowPkId(), 5, false);

        MappedRowDto[] mappedRowDtos = given().contentType(ContentType.JSON)
                .body(List.of(docId))
                .post(TABLE_PREFIX + "/mapped-coa")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(MappedRowDto[].class);

        Assertions.assertEquals(1, mappedRowDtos.length);
        MappedRowDto mappedRowDto = mappedRowDtos[0];
        CoaDataEntity coaDataEntity = coaDataRepository.findById(extractedRowCoaDataRepository.findByRowOptional(
                        ntaTable.getTableRows().get(0).getTableRowPkId().getTableId(),
                        ntaTable.getTableRows().get(0).getTableRowPkId().getRowId()).get().getExtractedTableRowCoaDataPkId()
                .getCoaDataId());
        Assertions.assertEquals(ntaTable.getTag(), mappedRowDto.tableType());
        Assertions.assertEquals(ntaRow.getParentText(), mappedRowDto.rowParent());
        Assertions.assertEquals(ntaRow.getCellsText().get(0), mappedRowDto.text());
        Assertions.assertEquals(fsRow.getParentText(), mappedRowDto.fsHeader());
        Assertions.assertEquals(fsRow.getCellsText().get(0), mappedRowDto.fsText());
        Assertions.assertEquals(coaDataEntity.getCoaId(), mappedRowDto.coaId());
    }

    @Test
    void testGetBlocks() {
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();
        int numBlocks = 3;
        List<LayoutBlockEntity> blocksForDoc1 = new ArrayList<>(createBlocks(docId1, numBlocks, false, false));
        List<LayoutBlockEntity> blocksForDoc2 = new ArrayList<>(createBlocks(docId2, numBlocks, false, false));
        blocksForDoc1.addAll(blocksForDoc2);
        List<ResponseDto.LayoutBlock> blocks = blocksForDoc1.stream()
                .map(lb -> layoutBlockEntityMapper.toDto(lb, Boolean.FALSE))
                .toList();
        List<ResponseDto.LayoutBlock> blocksForBlockIds = given().contentType(ContentType.JSON)
                .body(blocksForDoc1.stream().map(LayoutBlockEntity::getBlockId).toList())
                .post(LAYOUT_PREFIX + "/batch")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });
        Assertions.assertEquals(blocks, blocksForBlockIds);
    }

    @Transactional
    List<CoaMappingEntity> createCoaMappings(List<UUID> docIds) {
        List<CoaMappingEntity> coaMappingEntities = new ArrayList<>();

        for (int i = 0; i < docIds.size(); i++) {
            Random rand = new Random();
            CoaMappingEntity coaMappingEntity = new CoaMappingEntity();
            coaMappingEntity.setTableId(rand.nextInt(100));
            coaMappingEntity.setRowId((short) rand.nextInt(100));
            coaMappingEntity.setDocId(docIds.get(i));
            coaMappingEntity.setTableType("TableType" + i);
            coaMappingEntity.setRowParent("");
            coaMappingEntity.setText("Text" + i);
            coaMappingEntity.setFsHeader("fsHeader" + i);
            coaMappingEntity.setFsText("fsText" + i);
            coaMappingEntity.setCoaId(rand.nextInt(100));
            coaMappingEntity.setIsApproved(rand.nextBoolean());
            coaMappingEntities.add(coaMappingEntity);
        }
        coaMappingRepository.persist(coaMappingEntities);
        return coaMappingEntities;
    }

    @Test
    void testDeleteMappedCoa() {
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();

        List<CoaMappingEntity> coaMappingEntities = createCoaMappings(List.of(docId1, docId2));

        long deletedMappingCount = given().contentType(ContentType.JSON)
                .body(List.of(docId1, docId2))
                .delete(TABLE_PREFIX + "/mapped-coa")
                .then()
                .statusCode(200)
                .extract()
                .body().as(Long.class);

        Assertions.assertEquals(coaMappingEntities.size(), deletedMappingCount);
    }

    @Test
    void testGetMappedCoaRowsById() {
        UUID docId1 = UUID.randomUUID();
        UUID docId2 = UUID.randomUUID();

        List<CoaMappingEntity> coaMappingEntities = createCoaMappings(List.of(docId1, docId2));
        coaMappingEntities.sort(Comparator.comparing(CoaMappingEntity::getId));

        List<MappedRowDto> mappedCoaRows = given().contentType(ContentType.JSON)
                .body(coaMappingEntities.stream().map(CoaMappingEntity::getId).toList())
                .post(TABLE_PREFIX + "/mapped-coa/ids")
                .then()
                .statusCode(200)
                .extract()
                .body()
                .as(new TypeRef<>() {
                });
        mappedCoaRows.sort(Comparator.comparing(MappedRowDto::id));
        for (int i = 0; i < coaMappingEntities.size(); i++) {
            CoaMappingEntity coaMappingEntity = coaMappingEntities.get(i);
            MappedRowDto mappedCoaRow = mappedCoaRows.get(i);

            Assertions.assertEquals(coaMappingEntity.getId(), mappedCoaRow.id());
            Assertions.assertEquals(coaMappingEntity.getTableId(), mappedCoaRow.blockId());
            Assertions.assertEquals(coaMappingEntity.getRowId().intValue(), mappedCoaRow.rowId());
            Assertions.assertEquals(coaMappingEntity.getDocId(), mappedCoaRow.docId());
            Assertions.assertEquals(coaMappingEntity.getTableType(), mappedCoaRow.tableType());
            Assertions.assertEquals(coaMappingEntity.getRowParent(), mappedCoaRow.rowParent());
            Assertions.assertEquals(coaMappingEntity.getText(), mappedCoaRow.text());
            Assertions.assertEquals(coaMappingEntity.getFsHeader(), mappedCoaRow.fsHeader());
            Assertions.assertEquals(coaMappingEntity.getFsText(), mappedCoaRow.fsText());
            Assertions.assertEquals(coaMappingEntity.getCoaId(), mappedCoaRow.coaId());
        }
    }
}
