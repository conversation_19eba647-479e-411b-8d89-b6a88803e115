package com.walnut.vegaspread.extraction.resource;

import com.walnut.vegaspread.common.roles.Roles;
import com.walnut.vegaspread.extraction.entity.SubtotalEntity;
import com.walnut.vegaspread.extraction.model.SubtotalDto;
import com.walnut.vegaspread.extraction.repository.SubtotalRepository;
import com.walnut.vegaspread.extraction.service.ExchangeService;
import io.quarkus.test.InjectMock;
import io.quarkus.test.common.http.TestHTTPEndpoint;
import io.quarkus.test.junit.QuarkusTest;
import io.quarkus.test.security.TestSecurity;
import io.restassured.common.mapper.TypeRef;
import io.restassured.http.ContentType;
import jakarta.inject.Inject;
import jakarta.persistence.EntityManager;
import jakarta.transaction.Transactional;
import org.flywaydb.core.Flyway;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Random;
import java.util.stream.IntStream;

import static com.walnut.vegaspread.common.utils.TestSetup.truncateAllTables;
import static io.restassured.RestAssured.given;

@QuarkusTest
@TestHTTPEndpoint(SubtotalResource.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
class SubtotalResourceTest {

    @Inject
    SubtotalRepository subtotalRepository;
    @Inject
    Flyway flyway;
    @InjectMock
    ExchangeService exchangeService;

    @Inject
    EntityManager entityManager;

    @BeforeAll
    void setUpDb() {
        flyway.migrate();
    }

    @AfterEach
    @Transactional
    void clean() {
        truncateAllTables(entityManager);
    }

    @AfterAll
    void cleanDb() {
        flyway.clean();
    }

    @Transactional
    List<SubtotalEntity> addSubtotals(String coaClient, String client) {

        List<SubtotalEntity> subtotalEntities = IntStream.range(0, 10).mapToObj(i -> {
            SubtotalEntity subtotalEntity = new SubtotalEntity();
            subtotalEntity.setClient(client);
            subtotalEntity.setCoaClient(coaClient);
            subtotalEntity.setSubtotalName("subtotal" + i);
            subtotalEntity.setExcelRowNumber((short) i);
            return subtotalEntity;
        }).toList();
        subtotalRepository.persist(subtotalEntities);
        return subtotalEntities;
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testCreateSubtotal() {
        String coaClientName = "walnut";
        String clientName = "rcbc";
        List<SubtotalDto.CreateOrUpdate> subtotalCreateDtos = IntStream.range(0, 3)
                .mapToObj(id -> new SubtotalDto.CreateOrUpdate("subtotal" + id, id)).toList();
        given().contentType(ContentType.JSON)
                .queryParam("coaClient", coaClientName)
                .queryParam("client", clientName)
                .body(subtotalCreateDtos)
                .when()
                .post()
                .then().statusCode(200);
        List<SubtotalEntity> subtotals = subtotalRepository.findByCoaClientAndClient(coaClientName, clientName);
        for (int i = 0; i < subtotalCreateDtos.size(); i++) {
            SubtotalEntity subtotal = subtotals.get(i);
            SubtotalDto.CreateOrUpdate dto = subtotalCreateDtos.get(i);
            Assertions.assertEquals(coaClientName, subtotal.getCoaClient());
            Assertions.assertEquals(clientName, subtotal.getClient());
            Assertions.assertEquals(dto.excelRowNumber().shortValue(), subtotal.getExcelRowNumber());
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testCreateSubtotalWithInvalidClient() {
        String coaClientName = "client";
        String clientName = "client";
        List<SubtotalDto.CreateOrUpdate> subtotalCreateDtos = List.of(new SubtotalDto.CreateOrUpdate("subtotal1", 1));

        Map<String, String> error = given().contentType(ContentType.JSON)
                .queryParam("coaClient", coaClientName)
                .queryParam("client", clientName)
                .body(subtotalCreateDtos)
                .when()
                .post().then().statusCode(500)
                .extract().body().as(new TypeRef<>() {
                });
        Assertions.assertTrue(error.get("details").contains(" java.security.InvalidParameterException"));
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testUpdateSubtotals() {
        String coaClientName = "walnut";
        String clientName = "walnut";
        List<SubtotalEntity> subtotals = addSubtotals(coaClientName, clientName);

        SubtotalEntity subtotalForUpdate = subtotals.get(new Random().nextInt(subtotals.size() - 1));
        List<SubtotalDto.CreateOrUpdate> subtotalUpdateDtos = List.of(
                new SubtotalDto.CreateOrUpdate("newSubtotal", subtotalForUpdate.getExcelRowNumber().intValue()));

        given().contentType(ContentType.JSON)
                .queryParam("coaClient", coaClientName)
                .queryParam("client", clientName)
                .body(subtotalUpdateDtos)
                .when()
                .post().then().statusCode(200);
        for (SubtotalDto.CreateOrUpdate dto : subtotalUpdateDtos) {
            SubtotalEntity subtotalEntity = subtotalRepository.findByCoaClientAndClientAndExcelRowNumber(coaClientName,
                    clientName, dto.excelRowNumber().shortValue()).orElse(null);
            assert subtotalEntity != null;
            Assertions.assertEquals(coaClientName, subtotalEntity.getCoaClient());
            Assertions.assertEquals(clientName, subtotalEntity.getClient());
            Assertions.assertEquals(dto.excelRowNumber().shortValue(), subtotalEntity.getExcelRowNumber());
            Assertions.assertEquals(dto.subtotalName(), subtotalEntity.getSubtotalName());
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testDeleteSubtotals() {
        String coaClientName = "walnut";
        String clientName = "rcbc";
        List<SubtotalEntity> subtotals = addSubtotals(coaClientName, clientName);

        SubtotalEntity subtotalForUpdate = subtotals.get(new Random().nextInt(subtotals.size() - 1));
        List<SubtotalDto.CreateOrUpdate> subtotalUpdateDtos = List.of(
                new SubtotalDto.CreateOrUpdate(null, subtotalForUpdate.getExcelRowNumber().intValue()));

        given().contentType(ContentType.JSON)
                .queryParam("coaClient", coaClientName)
                .queryParam("client", clientName)
                .body(subtotalUpdateDtos)
                .when()
                .post().then().statusCode(200);
        for (SubtotalDto.CreateOrUpdate dto : subtotalUpdateDtos) {
            Optional<SubtotalEntity> subtotalEntity = subtotalRepository.findByCoaClientAndClientAndExcelRowNumber(
                    coaClientName,
                    clientName, dto.excelRowNumber().shortValue());
            Assertions.assertTrue(subtotalEntity.isEmpty());
        }
    }

    @Test
    @TestSecurity(user = "admin", roles = Roles.ADMIN)
    void testGetSubtotals() {
        String coaClientName = "walnut";
        String clientName = "rcbc";
        List<SubtotalEntity> subtotals = addSubtotals(coaClientName, clientName);

        given().contentType(ContentType.JSON)
                .queryParam("coaClient", coaClientName)
                .queryParam("client", clientName)
                .when()
                .get().then().statusCode(200);
        List<SubtotalEntity> subtotalEntity = subtotalRepository.findByCoaClientAndClient(coaClientName, clientName);
        for (int i = 0; i < subtotals.size(); i++) {
            Assertions.assertEquals(subtotals.get(i), subtotalEntity.get(i));
        }
    }
}

