BUILD_NUMBER=local

ENV=dev
SERVICE_NAME=audit
ENV_UPPER=$(shell echo ${ENV} | tr '[:lower:]' '[:upper:]')
GCLOUD_RUN_CPU=1
GCLOUD_RUN_MEM=512Mi
GCLOUD_RUN_CONCURRENCY=80
GCLOUD_RUN_MAX_INSTANCES=1
BUILD_DATE=$(shell date "+%d-%m-%y")
BUILD_ID=${ENV}-${BUILD_DATE}-${BUILD_NUMBER}
CLOUD_SQL_INSTANCE=vegaspread-7586a:asia-southeast1:vega-db
GCLOUD_RUN_MAX_INSTANCES=1
KEYCLOAK_REALM=dev-vega

deploy:
	gcloud artifacts docker images delete asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} --quiet || true
	docker tag thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME}
	docker push asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME}

	gcloud run deploy ${ENV}-vega-${SERVICE_NAME} \
		--image asia-southeast1-docker.pkg.dev/vegaspread-7586a/java/${ENV}-${SERVICE_NAME} \
		--allow-unauthenticated --port=8080 --cpu=${GCLOUD_RUN_CPU} --memory=${GCLOUD_RUN_MEM} \
		--max-instances=${GCLOUD_RUN_MAX_INSTANCES} --concurrency ${GCLOUD_RUN_CONCURRENCY} \
		--region=asia-southeast1 --cpu-boost --cpu-throttling \
		--set-env-vars=VEGA_ENV=${ENV} \
		--set-env-vars QUARKUS_OIDC_AUTH_SERVER_URL=https://auth.vegaspread.cloud/realms/${KEYCLOAK_REALM} \
		--set-env-vars CLOUD_SQL_INSTANCE=${CLOUD_SQL_INSTANCE} \
		--project=vegaspread-7586a --service-account=${ENV}-<EMAIL>

deploy-native:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth mvn clean package -Dnative
	docker build -f src/main/docker/Dockerfile.native-micro -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy

deploy-jvm:
	QUARKUS_SMALLRYE_OPENAPI_OAUTH2_IMPLICIT_AUTHORIZATION_URL=https://auth.vegaspread.cloud/realms/dev-vega/protocol/openid-connect/auth mvn clean package -Dmaven.test.skip=true
	docker build -f src/main/docker/Dockerfile.jvm -t thewalnutai/vega-${SERVICE_NAME}:${BUILD_ID} .
	make deploy
